name: shoot_z
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.2.0+4016 #1.2.0+4016

environment:
  sdk: '>=3.4.0 <4.0.0'

fluwx:
  app_id: 'wx7917adc785bc0eaf'
  debug_logging: true # Logging in debug mode.
  android:
  #    interrupt_wx_request: true # Defaults to true.
  #    flutter_activity: 'MainActivity' # Defaults to app's launcher
  ios:
    universal_link: https://f7b827c8aa8505f329cd379056b4ddc7.share2dlink.com
    #    scene_delegate: true # Defaults to false.
    no_pay: true # Set to true to disable payment.
#    ignore_security: true # Set to true to disable security seetings.

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  flutter_screenutil: 5.9.1
  cached_network_image: ^3.4.1
  flutter_native_splash: 2.4.0
  json_serializable: 6.7.1
  json_annotation: 4.8.1
  #xml: ^6.4.0 # 用于解析 XML
  tencentcloud_cos_sdk_plugin: ^1.2.0 # 腾讯云 COS 官方 Flutter SDK
  build_runner: ^2.4.13
  flutter_gen: any
  open_file: any
  flutter_inappwebview: ^6.1.5
  image_picker: any
  http_parser: any
  mime_type: any
  connectivity_plus: 6.1.1
  flutter_svg: any
  get: 4.6.6
  dio: ^5.4.3+1
  chewie: ^1.10.0
  video_player: ^2.9.3
  pin_code_fields: ^8.0.1
  share_plus: ^10.1.2
  carousel_slider: ^5.0.0
  smooth_page_indicator: ^1.2.0+3
  scrollable_positioned_list: ^0.3.8
  image_gallery_saver: ^2.0.3 # 用于保存视频到相册
  flutter_cupertino_datetime_picker: ^3.0.0
#  flutter_datetime_picker_plus: ^2.2.0
  flutter_easyloading: 3.0.5 #loading样式
  pull_to_refresh: 2.0.0 #加载
  map_launcher: ^3.5.0 #打开地图
  url_launcher: ^6.0.20
  flutter_swiper_plus: ^2.0.4 #轮播组件
  loading_indicator:
  geolocator: ^13.0.2
  geolocator_android: 4.6.1
  fluwx: ^5.3.1
  in_app_purchase: ^3.2.0
  flutter_secure_storage: ^9.2.2
  toggle_switch: ^2.3.0
  floor: ^1.5.0     #orm数据库
 #  sqlite3_flutter_libs: any # 提供 SQLite 支持
  collection: ^1.18.0  # 对日期进行分组
  encrypt: ^5.0.1
  flutter_html: ^3.0.0-beta.2
  device_info_plus:
    path: ./packages/device_info_plus-11.2.0 #获得设备信息
  package_info_plus: ^8.1.2 #获得版本信息 install_plugin: ^2.1.0
  android_package_installer: ^0.0.2
  ota_update: ^6.0.0
  path_provider: ^2.1.5
  progress_dialog_null_safe: ^3.0.0
  mime: ^1.0.0
#  xbr_gaode_navi_amap: ^3.0.0
#  visibility_detector: ^0.4.0+2
  google_fonts: ^6.2.1
  fl_chart: 0.69.2 #折线图
  decimal: ^3.0.2 #计算金额
# extended_nested_scroll_view: ^6.2.1
  city_pickers: ^1.3.0
  table_calendar: ^3.1.3
  better_player: ^0.0.84

  amap_flutter_location:
    path: ./packages/amap_flutter_location-3.0.0

  ui_packages:
    path: ./packages/ui_packages

  flutter_common:
    path: ./packages/common-packages

  utils_package:
    path: ./packages/utils_package

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner:
  flutter_gen_runner:
  frontend_server_client: ^4.0.0
  floor_generator: ^1.5.0
  intl_utils: ^2.8.7

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0
  leak_tracker: ^10.0.5

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter_gen:
  assets:
    outputs:
      class_name: WxAssets

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/3.0x/ic_launcher.png
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: DIN
      fonts:
        - asset: assets/fonts/DIN-Bold.otf
        - asset: assets/fonts/DIN-Regular.otf
          weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
flutter_intl:
  enabled: true
  main_locale: zh
