///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class MatchesInfoModelTeamsTeamScoreDetail {
/*
{
  "assistCount": 0,
  "defensiveReboundCount": 0,
  "freeThrowShootCount": 0,
  "freeThrowShootHit": 0,
  "freeThrowShootRate": "string",
  "markedScore": 0,
  "offensiveReboundCount": 0,
  "reboundCount": 0,
  "shootCount": 0,
  "shootHit": 0,
  "shootRate": "string",
  "threePointShootCount": 0,
  "threePointShootHit": 0,
  "threePointShootRate": "string",
  "totalScore": 0,
  "twoPointShootCount": 0,
  "twoPointShootHit": 0,
  "twoPointShootRate": "string"
} 
*/

  int? assistCount;
  int? defensiveReboundCount;
  int? freeThrowShootCount;
  int? freeThrowShootHit;
  String? freeThrowShootRate;
  int? markedScore;
  int? offensiveReboundCount;
  int? reboundCount;
  int? shootCount;
  int? shootHit;
  String? shootRate;
  int? threePointShootCount;
  int? threePointShootHit;
  String? threePointShootRate;
  int? totalScore;
  int? twoPointShootCount;
  int? twoPointShootHit;
  String? twoPointShootRate;

  MatchesInfoModelTeamsTeamScoreDetail({
    this.assistCount,
    this.defensiveReboundCount,
    this.freeThrowShootCount,
    this.freeThrowShootHit,
    this.freeThrowShootRate,
    this.markedScore,
    this.offensiveReboundCount,
    this.reboundCount,
    this.shootCount,
    this.shootHit,
    this.shootRate,
    this.threePointShootCount,
    this.threePointShootHit,
    this.threePointShootRate,
    this.totalScore,
    this.twoPointShootCount,
    this.twoPointShootHit,
    this.twoPointShootRate,
  });
  MatchesInfoModelTeamsTeamScoreDetail.fromJson(Map<String, dynamic> json) {
    assistCount = json['assistCount']?.toInt();
    defensiveReboundCount = json['defensiveReboundCount']?.toInt();
    freeThrowShootCount = json['freeThrowShootCount']?.toInt();
    freeThrowShootHit = json['freeThrowShootHit']?.toInt();
    freeThrowShootRate = json['freeThrowShootRate']?.toString();
    markedScore = json['markedScore']?.toInt();
    offensiveReboundCount = json['offensiveReboundCount']?.toInt();
    reboundCount = json['reboundCount']?.toInt();
    shootCount = json['shootCount']?.toInt();
    shootHit = json['shootHit']?.toInt();
    shootRate = json['shootRate']?.toString();
    threePointShootCount = json['threePointShootCount']?.toInt();
    threePointShootHit = json['threePointShootHit']?.toInt();
    threePointShootRate = json['threePointShootRate']?.toString();
    totalScore = json['totalScore']?.toInt();
    twoPointShootCount = json['twoPointShootCount']?.toInt();
    twoPointShootHit = json['twoPointShootHit']?.toInt();
    twoPointShootRate = json['twoPointShootRate']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['assistCount'] = assistCount;
    data['defensiveReboundCount'] = defensiveReboundCount;
    data['freeThrowShootCount'] = freeThrowShootCount;
    data['freeThrowShootHit'] = freeThrowShootHit;
    data['freeThrowShootRate'] = freeThrowShootRate;
    data['markedScore'] = markedScore;
    data['offensiveReboundCount'] = offensiveReboundCount;
    data['reboundCount'] = reboundCount;
    data['shootCount'] = shootCount;
    data['shootHit'] = shootHit;
    data['shootRate'] = shootRate;
    data['threePointShootCount'] = threePointShootCount;
    data['threePointShootHit'] = threePointShootHit;
    data['threePointShootRate'] = threePointShootRate;
    data['totalScore'] = totalScore;
    data['twoPointShootCount'] = twoPointShootCount;
    data['twoPointShootHit'] = twoPointShootHit;
    data['twoPointShootRate'] = twoPointShootRate;
    return data;
  }
}

class MatchesInfoModelTeamsTeamBestThreePointKing {
/*
{
  "assist": 0,
  "hit": 0,
  "locked": 0,
  "number": "string",
  "photo": "string",
  "playerId": "0",
  "rate": "string",
  "rebound": 0,
  "score": "string"
} 
*/

  int? assist;
  int? hit;
  int? locked;
  String? number;
  String? photo;
  String? playerId;
  String? rate;
  int? rebound;
  String? score;

  MatchesInfoModelTeamsTeamBestThreePointKing({
    this.assist,
    this.hit,
    this.locked,
    this.number,
    this.photo,
    this.playerId,
    this.rate,
    this.rebound,
    this.score,
  });
  MatchesInfoModelTeamsTeamBestThreePointKing.fromJson(
      Map<String, dynamic> json) {
    assist = json['assist']?.toInt();
    hit = json['hit']?.toInt();
    locked = json['locked']?.toInt();
    number = json['number']?.toString();
    photo = json['photo']?.toString();
    playerId = json['playerId']?.toString();
    rate = json['rate']?.toString();
    rebound = json['rebound']?.toInt();
    score = json['score']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['assist'] = assist;
    data['hit'] = hit;
    data['locked'] = locked;
    data['number'] = number;
    data['photo'] = photo;
    data['playerId'] = playerId;
    data['rate'] = rate;
    data['rebound'] = rebound;
    data['score'] = score;
    return data;
  }
}

class MatchesInfoModelTeamsTeamBestScoreKing {
/*
{
  "assist": 0,
  "hit": 0,
  "locked": 0,
  "number": "string",
  "photo": "string",
  "playerId": "0",
  "rate": "string",
  "rebound": 0,
  "score": "string"
} 
*/

  int? assist;
  int? hit;
  int? locked;
  String? number;
  String? photo;
  String? playerId;
  String? rate;
  int? rebound;
  String? score;
  MatchesInfoModelTeamsTeamBestScoreKing({
    this.assist,
    this.hit,
    this.locked,
    this.number,
    this.photo,
    this.playerId,
    this.rate,
    this.rebound,
    this.score,
  });
  MatchesInfoModelTeamsTeamBestScoreKing.fromJson(Map<String, dynamic> json) {
    assist = json['assist']?.toInt();
    hit = json['hit']?.toInt();
    locked = json['locked']?.toInt();
    number = json['number']?.toString();
    photo = json['photo']?.toString();
    playerId = json['playerId']?.toString();
    rate = json['rate']?.toString();
    rebound = json['rebound']?.toInt();
    score = json['score']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['assist'] = assist;
    data['hit'] = hit;
    data['locked'] = locked;
    data['number'] = number;
    data['photo'] = photo;
    data['playerId'] = playerId;
    data['rate'] = rate;
    data['rebound'] = rebound;
    data['score'] = score;
    return data;
  }
}

class MatchesInfoModelTeamsTeamBestMvp {
/*
{
  "assist": 0,
  "hit": 0,
  "locked": 0,
  "number": "string",
  "photo": "string",
  "playerId": "0",
  "rate": "string",
  "rebound": 0,
  "score": "string"
} 
*/

  int? assist;
  int? hit;
  int? locked;
  String? number;
  String? photo;
  String? playerId;
  String? rate;
  int? rebound;
  String? score;

  MatchesInfoModelTeamsTeamBestMvp({
    this.assist,
    this.hit,
    this.locked,
    this.number,
    this.photo,
    this.playerId,
    this.rate,
    this.rebound,
    this.score,
  });
  MatchesInfoModelTeamsTeamBestMvp.fromJson(Map<String, dynamic> json) {
    assist = json['assist']?.toInt();
    hit = json['hit']?.toInt();
    locked = json['locked']?.toInt();
    number = json['number']?.toString();
    photo = json['photo']?.toString();
    playerId = json['playerId']?.toString();
    rate = json['rate']?.toString();
    rebound = json['rebound']?.toInt();
    score = json['score']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['assist'] = assist;
    data['hit'] = hit;
    data['locked'] = locked;
    data['number'] = number;
    data['photo'] = photo;
    data['playerId'] = playerId;
    data['rate'] = rate;
    data['rebound'] = rebound;
    data['score'] = score;
    return data;
  }
}

class MatchesInfoModelTeamsTeamBest {
/*
{
  "assistKing": {
    "assist": 0,
    "hit": 0,
    "locked": 0,
    "number": "string",
    "photo": "string",
    "playerId": "0",
    "rate": "string",
    "rebound": 0,
    "score": "string"
  },
  "freeThrowKing": {
    "assist": 0,
    "hit": 0,
    "locked": 0,
    "number": "string",
    "photo": "string",
    "playerId": "0",
    "rate": "string",
    "rebound": 0,
    "score": "string"
  },
  "mvp": {
    "assist": 0,
    "hit": 0,
    "locked": 0,
    "number": "string",
    "photo": "string",
    "playerId": "0",
    "rate": "string",
    "rebound": 0,
    "score": "string"
  },
  "reboundKing": {
    "assist": 0,
    "hit": 0,
    "locked": 0,
    "number": "string",
    "photo": "string",
    "playerId": "0",
    "rate": "string",
    "rebound": 0,
    "score": "string"
  },
  "scoreKing": {
    "assist": 0,
    "hit": 0,
    "locked": 0,
    "number": "string",
    "photo": "string",
    "playerId": "0",
    "rate": "string",
    "rebound": 0,
    "score": "string"
  },
  "threePointKing": {
    "assist": 0,
    "hit": 0,
    "locked": 0,
    "number": "string",
    "photo": "string",
    "playerId": "0",
    "rate": "string",
    "rebound": 0,
    "score": "string"
  }
} 
*/

  MatchesInfoModelTeamsTeamBestScoreKing? assistKing;
  MatchesInfoModelTeamsTeamBestScoreKing? freeThrowKing;
  MatchesInfoModelTeamsTeamBestMvp? mvp;
  MatchesInfoModelTeamsTeamBestScoreKing? reboundKing;
  MatchesInfoModelTeamsTeamBestScoreKing? scoreKing;
  MatchesInfoModelTeamsTeamBestScoreKing? threePointKing;

  MatchesInfoModelTeamsTeamBest({
    this.assistKing,
    this.freeThrowKing,
    this.mvp,
    this.reboundKing,
    this.scoreKing,
    this.threePointKing,
  });
  MatchesInfoModelTeamsTeamBest.fromJson(Map<String, dynamic> json) {
    assistKing = (json['assistKing'] != null)
        ? MatchesInfoModelTeamsTeamBestScoreKing.fromJson(json['assistKing'])
        : null;
    freeThrowKing = (json['freeThrowKing'] != null)
        ? MatchesInfoModelTeamsTeamBestScoreKing.fromJson(json['freeThrowKing'])
        : null;
    mvp = (json['mvp'] != null)
        ? MatchesInfoModelTeamsTeamBestMvp.fromJson(json['mvp'])
        : null;
    reboundKing = (json['reboundKing'] != null)
        ? MatchesInfoModelTeamsTeamBestScoreKing.fromJson(json['reboundKing'])
        : null;
    scoreKing = (json['scoreKing'] != null)
        ? MatchesInfoModelTeamsTeamBestScoreKing.fromJson(json['scoreKing'])
        : null;
    threePointKing = (json['threePointKing'] != null)
        ? MatchesInfoModelTeamsTeamBestScoreKing.fromJson(
            json['threePointKing'])
        : null;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    if (assistKing != null) {
      data['assistKing'] = assistKing!.toJson();
    }
    if (freeThrowKing != null) {
      data['freeThrowKing'] = freeThrowKing!.toJson();
    }
    if (mvp != null) {
      data['mvp'] = mvp!.toJson();
    }
    if (reboundKing != null) {
      data['reboundKing'] = reboundKing!.toJson();
    }
    if (scoreKing != null) {
      data['scoreKing'] = scoreKing!.toJson();
    }
    if (threePointKing != null) {
      data['threePointKing'] = threePointKing!.toJson();
    }
    return data;
  }
}

class MatchesInfoModelTeams {
/*
{
  "currentScore": 0,
  "fragmentCount": 0,
  "locked": 0,
  "logo": "string",
  "maxOffsetScore": 0,
  "score": 0,
  "scoreTrend": [
    0
  ],
  "status": 0,
  "teamBest": {
    "assistKing": {
      "assist": 0,
      "hit": 0,
      "locked": 0,
      "number": "string",
      "photo": "string",
      "playerId": "0",
      "rate": "string",
      "rebound": 0,
      "score": "string"
    },
    "freeThrowKing": {
      "assist": 0,
      "hit": 0,
      "locked": 0,
      "number": "string",
      "photo": "string",
      "playerId": "0",
      "rate": "string",
      "rebound": 0,
      "score": "string"
    },
    "mvp": {
      "assist": 0,
      "hit": 0,
      "locked": 0,
      "number": "string",
      "photo": "string",
      "playerId": "0",
      "rate": "string",
      "rebound": 0,
      "score": "string"
    },
    "reboundKing": {
      "assist": 0,
      "hit": 0,
      "locked": 0,
      "number": "string",
      "photo": "string",
      "playerId": "0",
      "rate": "string",
      "rebound": 0,
      "score": "string"
    },
    "scoreKing": {
      "assist": 0,
      "hit": 0,
      "locked": 0,
      "number": "string",
      "photo": "string",
      "playerId": "0",
      "rate": "string",
      "rebound": 0,
      "score": "string"
    },
    "threePointKing": {
      "assist": 0,
      "hit": 0,
      "locked": 0,
      "number": "string",
      "photo": "string",
      "playerId": "0",
      "rate": "string",
      "rebound": 0,
      "score": "string"
    }
  },
  "teamId": "0",
  "teamName": "string",
  "teamScoreDetail": {
    "assistCount": 0,
    "defensiveReboundCount": 0,
    "freeThrowShootCount": 0,
    "freeThrowShootHit": 0,
    "freeThrowShootRate": "string",
    "markedScore": 0,
    "offensiveReboundCount": 0,
    "reboundCount": 0,
    "shootCount": 0,
    "shootHit": 0,
    "shootRate": "string",
    "threePointShootCount": 0,
    "threePointShootHit": 0,
    "threePointShootRate": "string",
    "totalScore": 0,
    "twoPointShootCount": 0,
    "twoPointShootHit": 0,
    "twoPointShootRate": "string"
  }
} 
*/

  int? currentScore;
  int? fragmentCount;
  int? locked;
  String? logo;
  int? maxOffsetScore;
  int? score;
  List<int?>? scoreTrend;
  int? status;
  MatchesInfoModelTeamsTeamBest? teamBest;
  String? teamId;
  String? teamName;
  MatchesInfoModelTeamsTeamScoreDetail? teamScoreDetail;

  MatchesInfoModelTeams({
    this.currentScore,
    this.fragmentCount,
    this.locked,
    this.logo,
    this.maxOffsetScore,
    this.score,
    this.scoreTrend,
    this.status,
    this.teamBest,
    this.teamId,
    this.teamName,
    this.teamScoreDetail,
  });
  MatchesInfoModelTeams.fromJson(Map<String, dynamic> json) {
    currentScore = json['currentScore']?.toInt();
    fragmentCount = json['fragmentCount']?.toInt();
    locked = json['locked']?.toInt();
    logo = json['logo']?.toString();
    maxOffsetScore = json['maxOffsetScore']?.toInt();
    score = json['score']?.toInt();
    if (json['scoreTrend'] != null) {
      final v = json['scoreTrend'];
      final arr0 = <int>[];
      v.forEach((v) {
        arr0.add(v.toInt());
      });
      scoreTrend = arr0;
    }
    status = json['status']?.toInt();
    teamBest = (json['teamBest'] != null)
        ? MatchesInfoModelTeamsTeamBest.fromJson(json['teamBest'])
        : null;
    teamId = json['teamId']?.toString();
    teamName = json['teamName']?.toString();
    teamScoreDetail = (json['teamScoreDetail'] != null)
        ? MatchesInfoModelTeamsTeamScoreDetail.fromJson(json['teamScoreDetail'])
        : null;
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['currentScore'] = currentScore;
    data['fragmentCount'] = fragmentCount;
    data['locked'] = locked;
    data['logo'] = logo;
    data['maxOffsetScore'] = maxOffsetScore;
    data['score'] = score;
    if (scoreTrend != null) {
      final v = scoreTrend;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['scoreTrend'] = arr0;
    }
    data['status'] = status;
    if (teamBest != null) {
      data['teamBest'] = teamBest!.toJson();
    }
    data['teamId'] = teamId;
    data['teamName'] = teamName;
    if (teamScoreDetail != null) {
      data['teamScoreDetail'] = teamScoreDetail!.toJson();
    }
    return data;
  }
}

class MatchesInfoModelHistory {
/*
{
  "leftScore": 0,
  "matchTime": "string",
  "rightScore": 0,
  "winner": 0
} 
*/

  int? leftScore;
  String? matchTime;
  int? rightScore;
  int? winner;

  MatchesInfoModelHistory({
    this.leftScore,
    this.matchTime,
    this.rightScore,
    this.winner,
  });
  MatchesInfoModelHistory.fromJson(Map<String, dynamic> json) {
    leftScore = json['leftScore']?.toInt();
    matchTime = json['matchTime']?.toString();
    rightScore = json['rightScore']?.toInt();
    winner = json['winner']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['leftScore'] = leftScore;
    data['matchTime'] = matchTime;
    data['rightScore'] = rightScore;
    data['winner'] = winner;
    return data;
  }
}

class MatchesInfoModelCourts {
/*
{
  "courtId": "0",
  "courtName": "string"
} 
*/

  String? courtId;
  String? courtName;

  MatchesInfoModelCourts({
    this.courtId,
    this.courtName,
  });
  MatchesInfoModelCourts.fromJson(Map<String, dynamic> json) {
    courtId = json['courtId']?.toString();
    courtName = json['courtName']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['courtId'] = courtId;
    data['courtName'] = courtName;
    return data;
  }
}

class MatchesInfoModel {
/*
{
  "ArenaLogo": "string",
  "arenaAliasName": "string",
  "arenaContact": "string",
  "arenaId": "0",
  "arenaName": "string",
  "courts": [
    {
      "courtId": "0",
      "courtName": "string"
    }
  ],
  "expired": true,
  "expiredTime": "string",
  "fullMatchPrice": "string",
  "halfMatchPrice": "string",
  "history": [
    {
      "leftScore": 0,
      "matchTime": "string",
      "rightScore": 0,
      "winner": 0
    }
  ],
  "joined": true,
  "lockStatus": 0,
  "locked": 0,
  "matchDate": "string",
  "matchEndTime": "string",
  "matchId": "0",
  "matchStartTime": "string",
  "matchSubscribe": true,
  "matchTime": "string",
  "matchVideoPath": "string",
  "matchWeek": "string",
  "status": 0,
  "summaryStatistics": true,
  "teams": [
    {
      "currentScore": 0,
      "fragmentCount": 0,
      "locked": 0,
      "logo": "string",
      "maxOffsetScore": 0,
      "score": 0,
      "scoreTrend": [
        0
      ],
      "status": 0,
      "teamBest": {
        "assistKing": {
          "assist": 0,
          "hit": 0,
          "locked": 0,
          "number": "string",
          "photo": "string",
          "playerId": "0",
          "rate": "string",
          "rebound": 0,
          "score": "string"
        },
        "freeThrowKing": {
          "assist": 0,
          "hit": 0,
          "locked": 0,
          "number": "string",
          "photo": "string",
          "playerId": "0",
          "rate": "string",
          "rebound": 0,
          "score": "string"
        },
        "mvp": {
          "assist": 0,
          "hit": 0,
          "locked": 0,
          "number": "string",
          "photo": "string",
          "playerId": "0",
          "rate": "string",
          "rebound": 0,
          "score": "string"
        },
        "reboundKing": {
          "assist": 0,
          "hit": 0,
          "locked": 0,
          "number": "string",
          "photo": "string",
          "playerId": "0",
          "rate": "string",
          "rebound": 0,
          "score": "string"
        },
        "scoreKing": {
          "assist": 0,
          "hit": 0,
          "locked": 0,
          "number": "string",
          "photo": "string",
          "playerId": "0",
          "rate": "string",
          "rebound": 0,
          "score": "string"
        },
        "threePointKing": {
          "assist": 0,
          "hit": 0,
          "locked": 0,
          "number": "string",
          "photo": "string",
          "playerId": "0",
          "rate": "string",
          "rebound": 0,
          "score": "string"
        }
      },
      "teamId": "0",
      "teamName": "string",
      "teamScoreDetail": {
        "assistCount": 0,
        "defensiveReboundCount": 0,
        "freeThrowShootCount": 0,
        "freeThrowShootHit": 0,
        "freeThrowShootRate": "string",
        "markedScore": 0,
        "offensiveReboundCount": 0,
        "reboundCount": 0,
        "shootCount": 0,
        "shootHit": 0,
        "shootRate": "string",
        "threePointShootCount": 0,
        "threePointShootHit": 0,
        "threePointShootRate": "string",
        "totalScore": 0,
        "twoPointShootCount": 0,
        "twoPointShootHit": 0,
        "twoPointShootRate": "string"
      }
    }
  ]
} 
*/

  String? ArenaLogo;
  String? arenaAliasName;
  String? arenaContact;
  String? arenaId;
  String? arenaName;
  List<MatchesInfoModelCourts?>? courts;
  bool? expired;
  String? expiredTime;
  String? fullMatchPrice;
  String? halfMatchPrice;
  List<MatchesInfoModelHistory?>? history;
  bool? joined;
  int? lockStatus;
  int? locked;
  String? matchDate;
  String? matchEndTime;
  String? matchId;
  String? matchStartTime;
  bool? matchSubscribe;
  String? matchTime;
  String? matchVideoPath;
  String? matchWeek;
  int? status;
  bool? summaryStatistics;
  List<MatchesInfoModelTeams?>? teams;

  MatchesInfoModel({
    this.ArenaLogo,
    this.arenaAliasName,
    this.arenaContact,
    this.arenaId,
    this.arenaName,
    this.courts,
    this.expired,
    this.expiredTime,
    this.fullMatchPrice,
    this.halfMatchPrice,
    this.history,
    this.joined,
    this.lockStatus,
    this.locked,
    this.matchDate,
    this.matchEndTime,
    this.matchId,
    this.matchStartTime,
    this.matchSubscribe,
    this.matchTime,
    this.matchVideoPath,
    this.matchWeek,
    this.status,
    this.summaryStatistics,
    this.teams,
  });
  MatchesInfoModel.fromJson(Map<String, dynamic> json) {
    ArenaLogo = json['ArenaLogo']?.toString();
    arenaAliasName = json['arenaAliasName']?.toString();
    arenaContact = json['arenaContact']?.toString();
    arenaId = json['arenaId']?.toString();
    arenaName = json['arenaName']?.toString();
    if (json['courts'] != null) {
      final v = json['courts'];
      final arr0 = <MatchesInfoModelCourts>[];
      v.forEach((v) {
        arr0.add(MatchesInfoModelCourts.fromJson(v));
      });
      courts = arr0;
    }
    expired = json['expired'];
    expiredTime = json['expiredTime']?.toString();
    fullMatchPrice = json['fullMatchPrice']?.toString();
    halfMatchPrice = json['halfMatchPrice']?.toString();
    if (json['history'] != null) {
      final v = json['history'];
      final arr0 = <MatchesInfoModelHistory>[];
      v.forEach((v) {
        arr0.add(MatchesInfoModelHistory.fromJson(v));
      });
      history = arr0;
    }
    joined = json['joined'];
    lockStatus = json['lockStatus']?.toInt();
    locked = json['locked']?.toInt();
    matchDate = json['matchDate']?.toString();
    matchEndTime = json['matchEndTime']?.toString();
    matchId = json['matchId']?.toString();
    matchStartTime = json['matchStartTime']?.toString();
    matchSubscribe = json['matchSubscribe'];
    matchTime = json['matchTime']?.toString();
    matchVideoPath = json['matchVideoPath']?.toString();
    matchWeek = json['matchWeek']?.toString();
    status = json['status']?.toInt();
    summaryStatistics = json['summaryStatistics'];
    if (json['teams'] != null) {
      final v = json['teams'];
      final arr0 = <MatchesInfoModelTeams>[];
      v.forEach((v) {
        arr0.add(MatchesInfoModelTeams.fromJson(v));
      });
      teams = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['ArenaLogo'] = ArenaLogo;
    data['arenaAliasName'] = arenaAliasName;
    data['arenaContact'] = arenaContact;
    data['arenaId'] = arenaId;
    data['arenaName'] = arenaName;
    if (courts != null) {
      final v = courts;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['courts'] = arr0;
    }
    data['expired'] = expired;
    data['expiredTime'] = expiredTime;
    data['fullMatchPrice'] = fullMatchPrice;
    data['halfMatchPrice'] = halfMatchPrice;
    if (history != null) {
      final v = history;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['history'] = arr0;
    }
    data['joined'] = joined;
    data['lockStatus'] = lockStatus;
    data['locked'] = locked;
    data['matchDate'] = matchDate;
    data['matchEndTime'] = matchEndTime;
    data['matchId'] = matchId;
    data['matchStartTime'] = matchStartTime;
    data['matchSubscribe'] = matchSubscribe;
    data['matchTime'] = matchTime;
    data['matchVideoPath'] = matchVideoPath;
    data['matchWeek'] = matchWeek;
    data['status'] = status;
    data['summaryStatistics'] = summaryStatistics;
    if (teams != null) {
      final v = teams;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['teams'] = arr0;
    }
    return data;
  }
}
