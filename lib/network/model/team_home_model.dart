///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class TeamHomeModelVideos {
/*
{
  "arenaId": "0",
  "duration": 0,
  "leftTeamId": "0",
  "leftTeamName": "string",
  "matchId": "0",
  "matchTime": "string",
  "rightTeamId": "0",
  "rightTeamName": "string",
  "title": "string",
  "videoCover": "string",
  "videoId": "0",
  "videoPath": "string"
} 
*/

  String? arenaId;
  int? duration;
  String? leftTeamId;
  String? leftTeamName;
  String? matchId;
  String? matchTime;
  String? rightTeamId;
  String? rightTeamName;
  String? title;
  String? videoCover;
  String? videoId;
  String? videoPath;

  TeamHomeModelVideos({
    this.arenaId,
    this.duration,
    this.leftTeamId,
    this.leftTeamName,
    this.matchId,
    this.matchTime,
    this.rightTeamId,
    this.rightTeamName,
    this.title,
    this.videoCover,
    this.videoId,
    this.videoPath,
  });
  TeamHomeModelVideos.fromJson(Map<String, dynamic> json) {
    arenaId = json['arenaId']?.toString();
    duration = json['duration']?.toInt();
    leftTeamId = json['leftTeamId']?.toString();
    leftTeamName = json['leftTeamName']?.toString();
    matchId = json['matchId']?.toString();
    matchTime = json['matchTime']?.toString();
    rightTeamId = json['rightTeamId']?.toString();
    rightTeamName = json['rightTeamName']?.toString();
    title = json['title']?.toString();
    videoCover = json['videoCover']?.toString();
    videoId = json['videoId']?.toString();
    videoPath = json['videoPath']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['arenaId'] = arenaId;
    data['duration'] = duration;
    data['leftTeamId'] = leftTeamId;
    data['leftTeamName'] = leftTeamName;
    data['matchId'] = matchId;
    data['matchTime'] = matchTime;
    data['rightTeamId'] = rightTeamId;
    data['rightTeamName'] = rightTeamName;
    data['title'] = title;
    data['videoCover'] = videoCover;
    data['videoId'] = videoId;
    data['videoPath'] = videoPath;
    return data;
  }
}

class TeamHomeModelScoreHistory {
/*
{
  "date": "string",
  "score": 0
} 
*/

  String? date;
  int? score;

  TeamHomeModelScoreHistory({
    this.date,
    this.score,
  });
  TeamHomeModelScoreHistory.fromJson(Map<String, dynamic> json) {
    date = json['date']?.toString();
    score = json['score']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['date'] = date;
    data['score'] = score;
    return data;
  }
}

class TeamHomeModelRankInfo {
/*
{
  "currentRank": 0,
  "highestRank": 0,
  "highestRankScore": 0,
  "rankScore": 0
} 
*/

  int? currentRank;
  int? highestRank;
  int? highestRankScore;
  int? rankScore;

  TeamHomeModelRankInfo({
    this.currentRank,
    this.highestRank,
    this.highestRankScore,
    this.rankScore,
  });
  TeamHomeModelRankInfo.fromJson(Map<String, dynamic> json) {
    currentRank = json['currentRank']?.toInt();
    highestRank = json['highestRank']?.toInt();
    highestRankScore = json['highestRankScore']?.toInt();
    rankScore = json['rankScore']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['currentRank'] = currentRank;
    data['highestRank'] = highestRank;
    data['highestRankScore'] = highestRankScore;
    data['rankScore'] = rankScore;
    return data;
  }
}

class TeamHomeModelMembers {
/*
{
  "memberId": "0",
  "userId": "0",
  "userName": "string",
  "userPhoto": "string"
} 
*/

  String? memberId;
  String? userId;
  String? userName;
  String? userPhoto;

  TeamHomeModelMembers({
    this.memberId,
    this.userId,
    this.userName,
    this.userPhoto,
  });
  TeamHomeModelMembers.fromJson(Map<String, dynamic> json) {
    memberId = json['memberId']?.toString();
    userId = json['userId']?.toString();
    userName = json['userName']?.toString();
    userPhoto = json['userPhoto']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['memberId'] = memberId;
    data['userId'] = userId;
    data['userName'] = userName;
    data['userPhoto'] = userPhoto;
    return data;
  }
}

class TeamHomeModel {
/*
{
  "applyStatus": 0,
  "arenaAliasName": "string",
  "arenaContact": "string",
  "arenaName": "string",
  "avgHit": "string",
  "avgScore": "string",
  "defaultTeam": true,
  "disbanded": true,
  "hasAuditApply": true,
  "hotScore": 0,
  "joined": true,
  "leader": true,
  "leaderUserId": 0,
  "loseCount": 0,
  "matchCount": 0,
  "memberCount": 0,
  "members": [
    {
      "memberId": "0",
      "userId": "0",
      "userName": "string",
      "userPhoto": "string"
    }
  ],
  "rankInfo": {
    "currentRank": 0,
    "highestRank": 0,
    "highestRankScore": 0,
    "rankScore": 0
  },
  "scoreHistory": [
    {
      "date": "string",
      "score": 0
    }
  ],
  "showId": "0",
  "teamDesc": "string",
  "teamId": "0",
  "teamLogo": "string",
  "teamName": "string",
  "teamPhoto": "string",
  "teamQrCode": "string",
  "videos": [
    {
      "arenaId": "0",
      "duration": 0,
      "leftTeamId": "0",
      "leftTeamName": "string",
      "matchId": "0",
      "matchTime": "string",
      "rightTeamId": "0",
      "rightTeamName": "string",
      "title": "string",
      "videoCover": "string",
      "videoId": "0",
      "videoPath": "string"
    }
  ],
  "winCount": 0,
  "winningStreak": 0
} 
*/

  int? applyStatus;
  String? arenaAliasName;
  String? arenaContact;
  String? arenaName;
  String? avgHit;
  String? avgScore;
  bool? defaultTeam;
  bool? disbanded;
  bool? hasAuditApply;
  int? hotScore;
  bool? joined;
  bool? leader;
  int? leaderUserId;
  int? loseCount;
  int? matchCount;
  int? memberCount;
  List<TeamHomeModelMembers?>? members;
  TeamHomeModelRankInfo? rankInfo;
  List<TeamHomeModelScoreHistory?>? scoreHistory;
  String? showId;
  String? teamDesc;
  String? teamId;
  String? teamLogo;
  String? teamName;
  String? teamPhoto;
  String? teamQrCode;
  List<TeamHomeModelVideos?>? videos;
  int? winCount;
  int? winningStreak;

  TeamHomeModel({
    this.applyStatus,
    this.arenaAliasName,
    this.arenaContact,
    this.arenaName,
    this.avgHit,
    this.avgScore,
    this.defaultTeam,
    this.disbanded,
    this.hasAuditApply,
    this.hotScore,
    this.joined,
    this.leader,
    this.leaderUserId,
    this.loseCount,
    this.matchCount,
    this.memberCount,
    this.members,
    this.rankInfo,
    this.scoreHistory,
    this.showId,
    this.teamDesc,
    this.teamId,
    this.teamLogo,
    this.teamName,
    this.teamPhoto,
    this.teamQrCode,
    this.videos,
    this.winCount,
    this.winningStreak,
  });
  TeamHomeModel.fromJson(Map<String, dynamic> json) {
    applyStatus = json['applyStatus']?.toInt();
    arenaAliasName = json['arenaAliasName']?.toString();
    arenaContact = json['arenaContact']?.toString();
    arenaName = json['arenaName']?.toString();
    avgHit = json['avgHit']?.toString();
    avgScore = json['avgScore']?.toString();
    defaultTeam = json['defaultTeam'];
    disbanded = json['disbanded'];
    hasAuditApply = json['hasAuditApply'];
    hotScore = json['hotScore']?.toInt();
    joined = json['joined'];
    leader = json['leader'];
    leaderUserId = json['leaderUserId']?.toInt();
    loseCount = json['loseCount']?.toInt();
    matchCount = json['matchCount']?.toInt();
    memberCount = json['memberCount']?.toInt();
    if (json['members'] != null) {
      final v = json['members'];
      final arr0 = <TeamHomeModelMembers>[];
      v.forEach((v) {
        arr0.add(TeamHomeModelMembers.fromJson(v));
      });
      members = arr0;
    }
    rankInfo = (json['rankInfo'] != null)
        ? TeamHomeModelRankInfo.fromJson(json['rankInfo'])
        : null;
    if (json['scoreHistory'] != null) {
      final v = json['scoreHistory'];
      final arr0 = <TeamHomeModelScoreHistory>[];
      v.forEach((v) {
        arr0.add(TeamHomeModelScoreHistory.fromJson(v));
      });
      scoreHistory = arr0;
    }
    showId = json['showId']?.toString();
    teamDesc = json['teamDesc']?.toString();
    teamId = json['teamId']?.toString();
    teamLogo = json['teamLogo']?.toString();
    teamName = json['teamName']?.toString();
    teamPhoto = json['teamPhoto']?.toString();
    teamQrCode = json['teamQrCode']?.toString();
    if (json['videos'] != null) {
      final v = json['videos'];
      final arr0 = <TeamHomeModelVideos>[];
      v.forEach((v) {
        arr0.add(TeamHomeModelVideos.fromJson(v));
      });
      videos = arr0;
    }
    winCount = json['winCount']?.toInt();
    winningStreak = json['winningStreak']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['applyStatus'] = applyStatus;
    data['arenaAliasName'] = arenaAliasName;
    data['arenaContact'] = arenaContact;
    data['arenaName'] = arenaName;
    data['avgHit'] = avgHit;
    data['avgScore'] = avgScore;
    data['defaultTeam'] = defaultTeam;
    data['disbanded'] = disbanded;
    data['hasAuditApply'] = hasAuditApply;
    data['hotScore'] = hotScore;
    data['joined'] = joined;
    data['leader'] = leader;
    data['leaderUserId'] = leaderUserId;
    data['loseCount'] = loseCount;
    data['matchCount'] = matchCount;
    data['memberCount'] = memberCount;
    if (members != null) {
      final v = members;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['members'] = arr0;
    }
    if (rankInfo != null) {
      data['rankInfo'] = rankInfo!.toJson();
    }
    if (scoreHistory != null) {
      final v = scoreHistory;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['scoreHistory'] = arr0;
    }
    data['showId'] = showId;
    data['teamDesc'] = teamDesc;
    data['teamId'] = teamId;
    data['teamLogo'] = teamLogo;
    data['teamName'] = teamName;
    data['teamPhoto'] = teamPhoto;
    data['teamQrCode'] = teamQrCode;
    if (videos != null) {
      final v = videos;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['videos'] = arr0;
    }
    data['winCount'] = winCount;
    data['winningStreak'] = winningStreak;
    return data;
  }
}
