///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class OptionSiteModelCourts {
/*
{
  "LatestCover": "string",
  "LatestDate": "string",
  "LatestTime": "string",
  "LatestVideoPath": "string",
  "courtCover": "string",
  "courtId": "0",
  "courtName": "string",
  "lastVideoId": "0",
  "optionDate": "string",
  "optionTime": "string"
} 
*/

  String? LatestCover;
  String? LatestDate;
  String? LatestTime;
  String? LatestVideoPath;
  String? courtCover;
  String? courtId;
  String? courtName;
  String? lastVideoId;
  String? optionDate;
  String? optionTime;

  OptionSiteModelCourts({
    this.LatestCover,
    this.LatestDate,
    this.LatestTime,
    this.LatestVideoPath,
    this.courtCover,
    this.courtId,
    this.courtName,
    this.lastVideoId,
    this.optionDate,
    this.optionTime,
  });
  OptionSiteModelCourts.fromJson(Map<String, dynamic> json) {
    LatestCover = json['LatestCover']?.toString();
    LatestDate = json['LatestDate']?.toString();
    LatestTime = json['LatestTime']?.toString();
    LatestVideoPath = json['LatestVideoPath']?.toString();
    courtCover = json['courtCover']?.toString();
    courtId = json['courtId']?.toString();
    courtName = json['courtName']?.toString();
    lastVideoId = json['lastVideoId']?.toString();
    optionDate = json['optionDate']?.toString();
    optionTime = json['optionTime']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['LatestCover'] = LatestCover;
    data['LatestDate'] = LatestDate;
    data['LatestTime'] = LatestTime;
    data['LatestVideoPath'] = LatestVideoPath;
    data['courtCover'] = courtCover;
    data['courtId'] = courtId;
    data['courtName'] = courtName;
    data['lastVideoId'] = lastVideoId;
    data['optionDate'] = optionDate;
    data['optionTime'] = optionTime;
    return data;
  }
}

class OptionSiteModel {
/*
{
  "arenaId": "0",
  "courts": [
    {
      "LatestCover": "string",
      "LatestDate": "string",
      "LatestTime": "string",
      "LatestVideoPath": "string",
      "courtCover": "string",
      "courtId": "0",
      "courtName": "string",
      "lastVideoId": "0",
      "optionDate": "string",
      "optionTime": "string"
    }
  ],
  "layoutImage": "string"
} 
*/

  String? arenaId;
  List<OptionSiteModelCourts?>? courts;
  String? layoutImage;

  OptionSiteModel({
    this.arenaId,
    this.courts,
    this.layoutImage,
  });
  OptionSiteModel.fromJson(Map<String, dynamic> json) {
    arenaId = json['arenaId']?.toString();
    if (json['courts'] != null) {
      final v = json['courts'];
      final arr0 = <OptionSiteModelCourts>[];
      v.forEach((v) {
        arr0.add(OptionSiteModelCourts.fromJson(v));
      });
      courts = arr0;
    }
    layoutImage = json['layoutImage']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['arenaId'] = arenaId;
    if (courts != null) {
      final v = courts;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['courts'] = arr0;
    }
    data['layoutImage'] = layoutImage;
    return data;
  }
}
