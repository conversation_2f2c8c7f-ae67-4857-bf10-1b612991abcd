///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class ArenaDetailsModelWarningContent {
/*
{
  "courtName": "string",
  "warningContent": "string"
} 
*/

  String? courtName;
  String? warningContent;

  ArenaDetailsModelWarningContent({
    this.courtName,
    this.warningContent,
  });
  ArenaDetailsModelWarningContent.fromJson(Map<String, dynamic> json) {
    courtName = json['courtName']?.toString();
    warningContent = json['warningContent']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['courtName'] = courtName;
    data['warningContent'] = warningContent;
    return data;
  }
}

class ArenaDetailsModelPath {
/*
{
  "path": "string",
  "type": 0
} 
*/

  String? path;
  int? type;

  ArenaDetailsModelPath({
    this.path,
    this.type,
  });
  ArenaDetailsModelPath.fromJson(Map<String, dynamic> json) {
    path = json['path']?.toString();
    type = json['type']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['path'] = path;
    data['type'] = type;
    return data;
  }
}

class ArenaDetailsModelConfigs {
/*
{
  "name": "string",
  "value": "string"
} 
*/

  String? name;
  String? value;

  ArenaDetailsModelConfigs({
    this.name,
    this.value,
  });
  ArenaDetailsModelConfigs.fromJson(Map<String, dynamic> json) {
    name = json['name']?.toString();
    value = json['value']?.toString();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name'] = name;
    data['value'] = value;
    return data;
  }
}

class ArenaDetailsModel {
/*
{
  "address": "string",
  "arenaID": 0,
  "arenaName": "string",
  "beginTime": "string",
  "configs": [
    {
      "name": "string",
      "value": "string"
    }
  ],
  "description": "string",
  "distance": 0,
  "endTime": "string",
  "floorCondition": [
    "string"
  ],
  "isBuyOut": true,
  "latitude": 0,
  "logo": "string",
  "longitude": 0,
  "path": [
    {
      "path": "string",
      "type": 0
    }
  ],
  "stage": 0,
  "tel": "string",
  "warningContent": [
    {
      "courtName": "string",
      "warningContent": "string"
    }
  ]
} 
*/

  String? address;
  int? arenaID;
  String? arenaName;
  String? beginTime;
  List<ArenaDetailsModelConfigs?>? configs;
  String? description;
  double? distance;
  String? endTime;
  List<String?>? floorCondition;
  bool? isBuyOut;
  double? latitude;
  String? logo;
  double? longitude;
  List<ArenaDetailsModelPath?>? path;
  int? stage;
  String? tel;
  List<ArenaDetailsModelWarningContent?>? warningContent;
  int? isPremium;

  ArenaDetailsModel(
      {this.address,
      this.arenaID,
      this.arenaName,
      this.beginTime,
      this.configs,
      this.description,
      this.distance,
      this.endTime,
      this.floorCondition,
      this.isBuyOut,
      this.latitude,
      this.logo,
      this.longitude,
      this.path,
      this.stage,
      this.tel,
      this.warningContent,
      this.isPremium});
  ArenaDetailsModel.fromJson(Map<String, dynamic> json) {
    address = json['address']?.toString();
    arenaID = json['arenaID']?.toInt();
    arenaName = json['arenaName']?.toString();
    beginTime = json['beginTime']?.toString();
    if (json['configs'] != null) {
      final v = json['configs'];
      final arr0 = <ArenaDetailsModelConfigs>[];
      v.forEach((v) {
        arr0.add(ArenaDetailsModelConfigs.fromJson(v));
      });
      configs = arr0;
    }
    description = json['description']?.toString();
    distance = json['distance']?.toDouble();
    endTime = json['endTime']?.toString();
    if (json['floorCondition'] != null) {
      final v = json['floorCondition'];
      final arr0 = <String>[];
      v.forEach((v) {
        arr0.add(v.toString());
      });
      floorCondition = arr0;
    }
    isBuyOut = json['isBuyOut'];
    latitude = json['latitude']?.toDouble();
    logo = json['logo']?.toString();
    longitude = json['longitude']?.toDouble();
    if (json['path'] != null) {
      final v = json['path'];
      final arr0 = <ArenaDetailsModelPath>[];
      v.forEach((v) {
        arr0.add(ArenaDetailsModelPath.fromJson(v));
      });
      path = arr0;
    }
    stage = json['stage']?.toInt();
    isPremium = json['isPremium']?.toInt();
    tel = json['tel']?.toString();
    if (json['warningContent'] != null) {
      final v = json['warningContent'];
      final arr0 = <ArenaDetailsModelWarningContent>[];
      v.forEach((v) {
        arr0.add(ArenaDetailsModelWarningContent.fromJson(v));
      });
      warningContent = arr0;
    }
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['address'] = address;
    data['arenaID'] = arenaID;
    data['arenaName'] = arenaName;
    data['beginTime'] = beginTime;
    if (configs != null) {
      final v = configs;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['configs'] = arr0;
    }
    data['description'] = description;
    data['distance'] = distance;
    data['endTime'] = endTime;
    if (floorCondition != null) {
      final v = floorCondition;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v);
      });
      data['floorCondition'] = arr0;
    }
    data['isBuyOut'] = isBuyOut;
    data['latitude'] = latitude;
    data['logo'] = logo;
    data['longitude'] = longitude;
    if (path != null) {
      final v = path;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['path'] = arr0;
    }
    data['stage'] = stage;
    data['isPremium'] = isPremium;
    data['tel'] = tel;
    if (warningContent != null) {
      final v = warningContent;
      final arr0 = [];
      v!.forEach((v) {
        arr0.add(v!.toJson());
      });
      data['warningContent'] = arr0;
    }
    return data;
  }
}
