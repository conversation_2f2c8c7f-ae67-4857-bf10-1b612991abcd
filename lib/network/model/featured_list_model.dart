///
/// Code generated by jsonToDartModel https://ashamp.github.io/jsonToDartModel/
///
class FeaturedListModel {
/*
{
  "content": "string",
  "coverUrl": "string",
  "createdTime": "string",
  "id": 0,
  "matchId": 0,
  "status": 0,
  "subTitle": "string",
  "title": "string",
  "type": 0,
  "updatedTime": "string",
  "video_url": "string",
  "weight": 0
} 
*/

  String? content;
  String? coverUrl;
  String? createdTime;
  int? id;
  int? matchId;
  int? status;
  String? subTitle;
  String? title;
  int? type;
  String? updatedTime;
  String? videoUrl;
  int? weight;

  FeaturedListModel({
    this.content,
    this.coverUrl,
    this.createdTime,
    this.id,
    this.matchId,
    this.status,
    this.subTitle,
    this.title,
    this.type,
    this.updatedTime,
    this.videoUrl,
    this.weight,
  });
  FeaturedListModel.fromJson(Map<String, dynamic> json) {
    content = json['content']?.toString();
    coverUrl = json['coverUrl']?.toString();
    createdTime = json['createdTime']?.toString();
    id = json['id']?.toInt();
    matchId = json['matchId']?.toInt();
    status = json['status']?.toInt();
    subTitle = json['subTitle']?.toString();
    title = json['title']?.toString();
    type = json['type']?.toInt();
    updatedTime = json['updatedTime']?.toString();
    videoUrl = json['video_url']?.toString();
    weight = json['weight']?.toInt();
  }
  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['content'] = content;
    data['coverUrl'] = coverUrl;
    data['createdTime'] = createdTime;
    data['id'] = id;
    data['matchId'] = matchId;
    data['status'] = status;
    data['subTitle'] = subTitle;
    data['title'] = title;
    data['type'] = type;
    data['updatedTime'] = updatedTime;
    data['video_url'] = videoUrl;
    data['weight'] = weight;
    return data;
  }
}
