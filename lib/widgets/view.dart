// ignore_for_file: sized_box_for_whitespace

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/my_super_widget.dart';
import 'package:shoot_z/widgets/mytext.dart';
import 'package:ui_packages/ui_packages.dart';

Widget buildFooter() => CustomFooter(
      builder: (BuildContext context, LoadStatus? mode) {
        Widget body;
        if (mode == LoadStatus.idle) {
          // body = Text("pull up load");
          body = const CupertinoActivityIndicator();
        } else if (mode == LoadStatus.loading) {
          body = const CupertinoActivityIndicator();
        } else if (mode == LoadStatus.failed) {
          // body = Text("Load Failed!Click retry!");
          body = const CupertinoActivityIndicator();
        } else if (mode == LoadStatus.canLoading) {
          // body = Text("release to load more");
          body = const CupertinoActivityIndicator();
        } else {
          body = MySuperWidget(
            text: "-- 我是有底线的～ --",
            textColor: const Color(0xFF999999),
            fontSize: 14.sp,
          );
          // body = SizedBox();
        }
        return Container(
          height: 20.w,
          child: Center(child: body),
        );
      },
    );

///下拉刷新的头部
ClassicHeader buildClassicHeader({
  Color color = Colors.grey,
}) {
  return ClassicHeader(
    height: 56.0,
    textStyle: TextStyle(color: color),
    releaseText: '松开刷新',
    releaseIcon: null,
    refreshingText: '正在刷新',
    refreshStyle: RefreshStyle.UnFollow,
    refreshingIcon: Container(
      height: 40,
      child: const LoadingIndicator(
        pathBackgroundColor: Colors.black26,
        indicatorType: Indicator.ballClipRotateMultiple,
      ),
    ),
    completeText: '刷新成功',
    completeIcon: null,
    // completeIcon: Container(
    //   height: 40,
    //   child: LoadingIndicator(
    //     color: Colors.black26,
    //     indicatorType: Indicator.ballClipRotateMultiple,
    //   ),
    // ),

    failedText: '刷新失败',
    failedIcon: null,
    //  failedIcon: Container(
    //   height: 40,
    //   child: LoadingIndicator(
    //     color: Colors.black26,
    //     indicatorType: Indicator.ballClipRotateMultiple,
    //   ),
    // ),
    idleText: '下拉刷新',
    idleIcon: null,
  );
}

///加载框
Widget buildLoad({
  ///大小
  double size = 10,
  double radius = 10,

  ///粗细
  double width = 2,

  ///是否居中
  bool isCenter = true,
  bool isShowGif = true,

  ///颜色
  Color color = Colors.black,
}) {
  if (isCenter) {
    // return Center(
    //   child: CupertinoActivityIndicator(radius: radius),
    // );
    return Center(
      child: Container(
        // height: 56,
        alignment: Alignment.center,
        constraints: BoxConstraints(minHeight: 150.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            !isShowGif
                ? const SizedBox(
                    width: 32,
                    height: 32,
                    child: LoadingIndicator(
                      // colors: [
                      //   Colors.redAccent,
                      // ],
                      // backgroundColor: Colors.red,
                      indicatorType: Indicator.ballRotateChase,
                    ),
                  )
                : MyImage(
                    'loading_gif.gif',
                    isAssetImage: true,
                    width: 80,
                    fit: BoxFit.fill,
                  ),
            // MyText('加载中...', color: Colors.white),
          ],
        ),
      ),
    );
  } else {
    // return CupertinoActivityIndicator(radius: radius);
    return Container(
      height: 56,
      alignment: Alignment.center,
      child: const LoadingIndicator(
        pathBackgroundColor: Colors.black26,
        indicatorType: Indicator.ballClipRotateMultiple,
      ),
    );
  }
}

Widget myNoDataView(BuildContext context,
    {String? msg,
    Widget? imagewidget,
    Color? textColor = Colours.color9393A5, //color5C5C6E
    double? height = 25,
    EdgeInsetsGeometry? margin}) {
  return Container(
    margin: margin,
    alignment: Alignment.center,
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.max,
      children: [
        imagewidget ??
            WxAssets.images.noDataArena2.image(width: 150.w, height: 150.w),
        SizedBox(
          height: height,
        ),
        MyText(
          msg == null ? "~${S.current.No_data_available}~" : "$msg",
          size: 14,
          color: textColor,
        ),
        SizedBox(
          height: 10.w,
        ),
      ],
    ),
  );
}

///底部悬浮菜单
showSheet(
  BuildContext context, {
  List<Widget> children = const <Widget>[],
  Widget? builder,
}) {
  return showDialog(
    context: context,
    barrierColor: Colors.transparent,
    barrierDismissible: false,
    builder: (_) {
      if (builder != null) {
        return builder;
      } else {
        return ClipRRect(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
          child: Container(
            color: Colors.white,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: children,
            ),
          ),
        );
      }
    },
  );
}

Widget buildRowTitleWidget(String name,
    {Widget? leftWidget,
    Widget? rightWidget,
    String? rightName,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    double fontSize = 16,
    double height = 40,
    Function()? rightOnTap}) {
  return Container(
    width: double.infinity,
    height: height.w,
    padding: padding ?? EdgeInsets.only(left: 15.w, right: 15.w),
    child: Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Stack(
          alignment: Alignment.centerLeft,
          children: [
            Positioned(
              top: 8.w,
              right: 10.w,
              child: WxAssets.images.titleTag.image(width: 12.w, height: 8.w),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Text(
                "$name\t\t\t\t",
                style: TextStyles.bold.copyWith(fontSize: fontSize.sp),
              ),
            ),
          ],
        ),
        if (leftWidget != null) leftWidget,
        const Spacer(),
        if (rightWidget != null) rightWidget,
        if (rightOnTap != null)
          InkWell(
            onTap: () {
              Future.delayed(const Duration(milliseconds: 50), rightOnTap);
            },
            child: Container(
              height: 40.w,
              constraints:
                  BoxConstraints(minWidth: leftWidget != null ? 60.w : 100.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    rightName ?? "",
                    style: TextStyle(color: Colours.white, fontSize: 12.sp),
                  ),
                  SizedBox(
                    width: 3.w,
                  ),
                  WxAssets.images.icArrowRight
                      .image(width: 14.w, height: 14.w, color: Colours.white),
                ],
              ),
            ),
          ),
      ],
    ),
  );
}

Widget indicatorLoading() {
  return const Center(
      child: CupertinoActivityIndicator(
    color: Colors.white,
  ));
}

Widget opaqueVisibility(visible) {
  return Visibility(
    visible: visible,
    child: Positioned.fill(
        child: GestureDetector(
      behavior: HitTestBehavior.opaque,
    )),
  );
}

//弹窗
void getMyDialog(
  String titleText,
  String btnText1,
  void Function()? onPressed1, {
  String btnText2 = "",
  void Function()? onPressed2,
  bool isShowClose = true,
  String imageAsset = "",
  String content = "",
  Widget? contentWidget,
  Widget? bottomHintWidget,
  double? imgWidth,
  double? imgHeight,
  bool btnIsHorizontal = false,
}) {
  Get.dialog(
    Padding(
      padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
      child: Material(
        type: MaterialType.transparency,
        color: Colors.transparent,
        child: Center(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  color: Colors.transparent,
                  child: Column(
                    children: <Widget>[
                      if (imageAsset != "")
                        SizedBox(
                          height: 30.w,
                        ),
                      SizedBox(
                        height: imageAsset != "" ? 100.w : 45.w,
                        width: double.infinity,
                        child: Stack(
                          alignment: Alignment.bottomCenter,
                          children: [
                            Container(
                              height: imageAsset != "" ? 70.w : 30.w,
                              width: double.infinity,
                              margin: EdgeInsets.only(
                                  top: imageAsset != "" ? 30.w : 15.w),
                              decoration: BoxDecoration(
                                color: Colours.color191921,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(25.r),
                                  topRight: Radius.circular(25.r),
                                ),
                              ),
                            ),
                            if (imageAsset != "")
                              Transform.translate(
                                offset: Offset(0, -25.w),
                                child: MyImage(
                                  imageAsset,
                                  width: imgWidth ?? 85.w,
                                  height: imgHeight ?? 85.w,
                                  isAssetImage: true,
                                  fit: BoxFit.fitWidth,
                                  bgColor: Colors.transparent,
                                ),
                              ),
                          ],
                        ),
                      ),
                      Transform.translate(
                        offset: const Offset(0, -10),
                        child: Container(
                          alignment: Alignment.topLeft,
                          // constraints: BoxConstraints(
                          //   maxHeight: titleText != "" ? 300.w : 260.w,
                          //   minHeight: 235.w,
                          // ),
                          decoration: BoxDecoration(
                            color: Colours.color191921,
                            borderRadius: BorderRadius.only(
                              bottomLeft: Radius.circular(25.r),
                              bottomRight: Radius.circular(25.r),
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          width: double.infinity,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              if (titleText != "")
                                Text(titleText,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 18.sp,
                                      color: Colours.white,
                                      fontWeight: AppFontWeight.semiBold(),
                                      height: 1,
                                    )),
                              if (titleText != "")
                                SizedBox(
                                  height: 20.w,
                                ),
                              if (content != "")
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 30.w),
                                  child: Text(content,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        color: Colours.color9393A5,
                                        height: 1.5,
                                        fontWeight: AppFontWeight.regular(),
                                      )),
                                ),
                              if (contentWidget != null) contentWidget,
                              if (content != "" || contentWidget != null)
                                SizedBox(
                                  height: 10.w,
                                ),
                              if (!btnIsHorizontal)
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: onPressed1,
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                      right: 25.w,
                                      top: 15.w,
                                      left: 25.w,
                                    ),
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 3.w,
                                        bottom: 3.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color282735,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                      gradient: const LinearGradient(
                                        colors: [
                                          Colours.color7732ED,
                                          Colours.colorA555EF
                                        ],
                                        begin: Alignment.bottomLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Text(
                                      btnText1,
                                      style: TextStyles.regular
                                          .copyWith(fontSize: 15.sp),
                                    ),
                                  ),
                                ),
                              if (btnText2 != "" && (!btnIsHorizontal))
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: onPressed2,
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                      right: 25.w,
                                      top: 15.w,
                                      left: 25.w,
                                    ),
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 3.w,
                                        bottom: 3.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color22222D,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                    ),
                                    child: Text(
                                      btnText2,
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 15.sp,
                                          color: Colours.color9393A5),
                                    ),
                                  ),
                                ),
                              if (btnIsHorizontal)
                                Row(
                                  children: [
                                    if (btnText2 != "")
                                      Expanded(
                                        child: GestureDetector(
                                          behavior: HitTestBehavior.translucent,
                                          onTap: onPressed2,
                                          child: Container(
                                            height: 46.w,
                                            width: double.infinity,
                                            alignment: Alignment.center,
                                            margin: EdgeInsets.only(
                                              left: 20.w,
                                              top: 15.w,
                                              right: 7.5.w,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colours.color22222D,
                                              border: Border.all(
                                                color: Colours.white,
                                                width: 1,
                                              ),
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(28.r)),
                                            ),
                                            child: Text(
                                              btnText2,
                                              style: TextStyles.bold.copyWith(
                                                  fontSize: 14.sp,
                                                  color: Colours.white),
                                            ),
                                          ),
                                        ),
                                      ),
                                    Expanded(
                                      child: GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: onPressed1,
                                        child: Container(
                                          height: 46.w,
                                          width: double.infinity,
                                          alignment: Alignment.center,
                                          margin: EdgeInsets.only(
                                            left: 7.5.w,
                                            top: 15.w,
                                            right: 20.w,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Colours.color282735,
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(28.r)),
                                            gradient: const LinearGradient(
                                              colors: [
                                                Colours.color7732ED,
                                                Colours.colorA555EF
                                              ],
                                              begin: Alignment.bottomLeft,
                                              end: Alignment.bottomRight,
                                            ),
                                          ),
                                          child: Text(
                                            btnText1,
                                            style: TextStyles.semiBold
                                                .copyWith(fontSize: 14.sp),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              if (bottomHintWidget != null)
                                const SizedBox(
                                  height: 20,
                                ),
                              if (bottomHintWidget != null) bottomHintWidget,
                              SizedBox(
                                height: 15.w,
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 25.w,
                      ),
                      if (isShowClose)
                        GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            AppPage.back();
                          },
                          child: WxAssets.images.icCloseDialog
                              .image(width: 30.w, height: 30.w),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ),
    barrierColor: Colors.black.withOpacity(0.85),
  );
}

//弹窗
void getMyDialog2(
  String titleText,
  String btnText1,
  void Function()? onPressed1, {
  String btnText2 = "",
  void Function()? onPressed2,
  bool isShowClose = true,
  String imageAsset = "",
  String content = "",
  Widget? contentWidget,
  Widget? bottomHintWidget,
  Widget? bottomBtnWidget,
  double? imgWidth,
  double? imgHeight,
  double contentTopRadius = 0,
  Color contentColor = Colours.white,
  bool btnIsHorizontal = false,
}) {
  Get.dialog(
    Padding(
      padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 30.w),
      child: Material(
        type: MaterialType.transparency,
        color: Colors.transparent,
        child: Center(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  color: Colors.transparent,
                  child: Column(
                    children: <Widget>[
                      Stack(
                        alignment: Alignment.bottomCenter,
                        children: [
                          if (imageAsset != "")
                            MyImage(
                              imageAsset,
                              width: imgWidth ?? 85.w,
                              height: imgHeight ?? 85.w,
                              isAssetImage: true,
                              fit: BoxFit.fitWidth,
                              bgColor: Colors.transparent,
                            ),
                        ],
                      ),
                      Transform.translate(
                        offset: const Offset(0, -1),
                        child: Container(
                          alignment: Alignment.topLeft,
                          // constraints: BoxConstraints(
                          //   maxHeight: titleText != "" ? 300.w : 260.w,
                          //   minHeight: 235.w,
                          // ),
                          decoration: BoxDecoration(
                            color: contentColor,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(contentTopRadius),
                              topRight: Radius.circular(contentTopRadius),
                              bottomLeft: Radius.circular(contentTopRadius == 0
                                  ? 16.r
                                  : contentTopRadius),
                              bottomRight: Radius.circular(contentTopRadius == 0
                                  ? 16.r
                                  : contentTopRadius),
                            ),
                          ),
                          width: double.infinity,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              if (titleText != "")
                                Text(titleText,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 18.sp,
                                      color: Colours.white,
                                      fontWeight: AppFontWeight.semiBold(),
                                      height: 1,
                                    )),
                              if (titleText != "")
                                SizedBox(
                                  height: 20.w,
                                ),
                              if (content != "")
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 30.w),
                                  child: Text(content,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        color: Colours.color9393A5,
                                        height: 1.5,
                                        fontWeight: AppFontWeight.regular(),
                                      )),
                                ),
                              if (contentWidget != null) contentWidget,
                              if (content != "" || contentWidget != null)
                                SizedBox(
                                  height: 10.w,
                                ),
                              if (!btnIsHorizontal)
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: onPressed1,
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                      right: 25.w,
                                      top: 15.w,
                                      left: 25.w,
                                    ),
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 3.w,
                                        bottom: 3.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color282735,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                      gradient: const LinearGradient(
                                        colors: [
                                          Colours.color7732ED,
                                          Colours.colorA555EF
                                        ],
                                        begin: Alignment.bottomLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Text(
                                      btnText1,
                                      style: TextStyles.regular
                                          .copyWith(fontSize: 15.sp),
                                    ),
                                  ),
                                ),
                              if (btnText2 != "" && (!btnIsHorizontal))
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: onPressed2,
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                      right: 25.w,
                                      top: 15.w,
                                      left: 25.w,
                                    ),
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 3.w,
                                        bottom: 3.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color22222D,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                    ),
                                    child: Text(
                                      btnText2,
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 15.sp,
                                          color: Colours.color9393A5),
                                    ),
                                  ),
                                ),
                              if (btnIsHorizontal && bottomBtnWidget == null)
                                Row(
                                  children: [
                                    if (btnText2 != "")
                                      Expanded(
                                        child: GestureDetector(
                                          behavior: HitTestBehavior.translucent,
                                          onTap: onPressed2,
                                          child: Container(
                                            height: 46.w,
                                            width: double.infinity,
                                            alignment: Alignment.center,
                                            margin: EdgeInsets.only(
                                              left: 20.w,
                                              top: 15.w,
                                              right: 7.5.w,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colours.color22222D,
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(28.r)),
                                            ),
                                            child: Text(
                                              btnText2,
                                              style: TextStyles.regular
                                                  .copyWith(
                                                      fontSize: 15.sp,
                                                      color:
                                                          Colours.color9393A5),
                                            ),
                                          ),
                                        ),
                                      ),
                                    Expanded(
                                      child: GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: onPressed1,
                                        child: Container(
                                          height: 46.w,
                                          width: double.infinity,
                                          alignment: Alignment.center,
                                          margin: EdgeInsets.only(
                                            left: 7.5.w,
                                            top: 15.w,
                                            right: 20.w,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Colours.color282735,
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(28.r)),
                                            gradient: const LinearGradient(
                                              colors: [
                                                Colours.color7732ED,
                                                Colours.colorA555EF
                                              ],
                                              begin: Alignment.bottomLeft,
                                              end: Alignment.bottomRight,
                                            ),
                                          ),
                                          child: Text(
                                            btnText1,
                                            style: TextStyles.regular
                                                .copyWith(fontSize: 15.sp),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              if (bottomBtnWidget != null) bottomBtnWidget,
                              if (bottomHintWidget != null)
                                const SizedBox(
                                  height: 20,
                                ),
                              if (bottomHintWidget != null) bottomHintWidget,
                              SizedBox(
                                height: 15.w,
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 25.w,
                      ),
                      if (isShowClose)
                        GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            AppPage.back();
                          },
                          child: WxAssets.images.icCloseDialog
                              .image(width: 30.w, height: 30.w),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ),
    barrierColor: Colors.black.withOpacity(0.85),
  );
}

//弹窗
void getMyDialog3(
  String titleText,
  String btnText1,
  void Function()? onPressed1, {
  String btnText2 = "",
  void Function()? onPressed2,
  bool isShowClose = true,
  String imageAsset = "",
  String content = "",
  Widget? contentWidget,
  Widget? bottomHintWidget,
  Widget? bottomBtnWidget,
  double? imgWidth,
  double? imgHeight,
  double padHorizontal = 30,
  bool btnIsHorizontal = false,
}) {
  Get.dialog(
    Padding(
      padding:
          EdgeInsets.symmetric(vertical: 24.w, horizontal: padHorizontal.w),
      child: Material(
        type: MaterialType.transparency,
        color: Colors.transparent,
        child: Center(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  color: Colors.transparent,
                  child: Column(
                    children: <Widget>[
                      if (imageAsset != "")
                        SizedBox(
                          height: 30.w,
                        ),
                      SizedBox(
                        height: imageAsset != "" ? 100.w : 45.w,
                        width: double.infinity,
                        child: Stack(
                          alignment: Alignment.bottomCenter,
                          children: [
                            Container(
                              height: imageAsset != "" ? 85.w : 30.w,
                              width: double.infinity,
                              margin: EdgeInsets.only(
                                  top: imageAsset != "" ? 30.w : 15.w),
                              decoration: BoxDecoration(
                                color: Colours.white,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(25.r),
                                  topRight: Radius.circular(25.r),
                                ),
                              ),
                            ),
                            if (imageAsset != "")
                              Transform.translate(
                                offset: Offset(0, -25.w),
                                child: MyImage(
                                  imageAsset,
                                  width: imgWidth ?? 85.w,
                                  height: imgHeight ?? 85.w,
                                  isAssetImage: true,
                                  fit: BoxFit.fitWidth,
                                  bgColor: Colors.transparent,
                                ),
                              ),
                          ],
                        ),
                      ),
                      Transform.translate(
                        offset: const Offset(0, -25),
                        child: Container(
                          alignment: Alignment.topLeft,
                          // constraints: BoxConstraints(
                          //   maxHeight: titleText != "" ? 300.w : 260.w,
                          //   minHeight: 235.w,
                          // ),
                          decoration: BoxDecoration(
                            color: Colours.white,
                            borderRadius: BorderRadius.only(
                              bottomLeft: Radius.circular(25.r),
                              bottomRight: Radius.circular(25.r),
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          width: double.infinity,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              if (titleText != "")
                                Text(titleText,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 18.sp,
                                      color: Colours.white,
                                      fontWeight: AppFontWeight.semiBold(),
                                      height: 1,
                                    )),
                              if (titleText != "")
                                SizedBox(
                                  height: 20.w,
                                ),
                              if (content != "")
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 30.w),
                                  child: Text(content,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        color: Colours.color9393A5,
                                        height: 1.5,
                                        fontWeight: AppFontWeight.regular(),
                                      )),
                                ),
                              if (contentWidget != null) contentWidget,
                              if (content != "" || contentWidget != null)
                                SizedBox(
                                  height: 10.w,
                                ),
                              if (!btnIsHorizontal)
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: onPressed1,
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                      right: 25.w,
                                      top: 15.w,
                                      left: 25.w,
                                    ),
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 3.w,
                                        bottom: 3.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color282735,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                      gradient: const LinearGradient(
                                        colors: [
                                          Colours.color7732ED,
                                          Colours.colorA555EF
                                        ],
                                        begin: Alignment.bottomLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                    ),
                                    child: Text(
                                      btnText1,
                                      style: TextStyles.regular
                                          .copyWith(fontSize: 15.sp),
                                    ),
                                  ),
                                ),
                              if (btnText2 != "" && (!btnIsHorizontal))
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: onPressed2,
                                  child: Container(
                                    height: 46.w,
                                    width: double.infinity,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                      right: 25.w,
                                      top: 15.w,
                                      left: 25.w,
                                    ),
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 3.w,
                                        bottom: 3.w),
                                    decoration: BoxDecoration(
                                      color: Colours.color22222D,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                    ),
                                    child: Text(
                                      btnText2,
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 15.sp,
                                          color: Colours.color9393A5),
                                    ),
                                  ),
                                ),
                              if (btnIsHorizontal && bottomBtnWidget == null)
                                Row(
                                  children: [
                                    if (btnText2 != "")
                                      Expanded(
                                        child: GestureDetector(
                                          behavior: HitTestBehavior.translucent,
                                          onTap: onPressed2,
                                          child: Container(
                                            height: 46.w,
                                            width: double.infinity,
                                            alignment: Alignment.center,
                                            margin: EdgeInsets.only(
                                              left: 20.w,
                                              top: 15.w,
                                              right: 7.5.w,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Colours.color22222D,
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(28.r)),
                                            ),
                                            child: Text(
                                              btnText2,
                                              style: TextStyles.regular
                                                  .copyWith(
                                                      fontSize: 15.sp,
                                                      color:
                                                          Colours.color9393A5),
                                            ),
                                          ),
                                        ),
                                      ),
                                    Expanded(
                                      child: GestureDetector(
                                        behavior: HitTestBehavior.translucent,
                                        onTap: onPressed1,
                                        child: Container(
                                          height: 46.w,
                                          width: double.infinity,
                                          alignment: Alignment.center,
                                          margin: EdgeInsets.only(
                                            left: 7.5.w,
                                            top: 15.w,
                                            right: 20.w,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Colours.color282735,
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(28.r)),
                                            gradient: const LinearGradient(
                                              colors: [
                                                Colours.color7732ED,
                                                Colours.colorA555EF
                                              ],
                                              begin: Alignment.bottomLeft,
                                              end: Alignment.bottomRight,
                                            ),
                                          ),
                                          child: Text(
                                            btnText1,
                                            style: TextStyles.regular
                                                .copyWith(fontSize: 15.sp),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              if (bottomBtnWidget != null) bottomBtnWidget,
                              if (bottomHintWidget != null)
                                const SizedBox(
                                  height: 20,
                                ),
                              if (bottomHintWidget != null) bottomHintWidget,
                              SizedBox(
                                height: 15.w,
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 25.w,
                      ),
                      if (isShowClose)
                        GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            AppPage.back();
                          },
                          child: WxAssets.images.icCloseDialog
                              .image(width: 30.w, height: 30.w),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ),
    barrierColor: Colors.black.withOpacity(0.85),
  );
}

/// 显示收货地址输入弹窗
/// [itemName] 商品名称
/// [pointsRequired] 所需积分
/// [onExchange] 点击兑换按钮的回调，返回手机号和地址
void showAddressDialog({
  required String itemName,
  required int pointsRequired,
  required Function(String phone, String address) onExchange,
}) {
  final phoneController = TextEditingController();
  final addressController = TextEditingController();

  Get.dialog(
    Material(
      type: MaterialType.transparency,
      color: Colors.transparent,
      child: Center(
        child: ClipRRect(
          borderRadius: BorderRadius.circular(25.r),
          child: Container(
            width: 315.w,
            color: Colours.color191921,
            padding: EdgeInsets.only(
                left: 20.w, right: 20.w, top: 40.w, bottom: 30.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                // 标题
                Center(
                  child: Text(
                    "请填写收货信息",
                    style: TextStyle(
                      fontSize: 18.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      height: 1,
                    ),
                  ),
                ),
                SizedBox(height: 25.w),

                // 商品信息
                RichText(
                  textAlign: TextAlign.left,
                  text: TextSpan(
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colours.color9393A5,
                      fontWeight: FontWeight.bold,
                    ),
                    children: [
                      const TextSpan(text: "您正在兑换 "),
                      TextSpan(
                        text: "$itemName（$pointsRequired积分）",
                        style: const TextStyle(
                          color: Colours.colorA44EFF,
                        ),
                      ),
                      const TextSpan(text: "，请填写以下信息："),
                    ],
                  ),
                ).paddingOnly(left: 5.w),
                SizedBox(height: 22.w),

                // 手机号输入框
                Container(
                  height: 54.w,
                  decoration: BoxDecoration(
                    color: Colours.color22222D,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  margin: EdgeInsets.symmetric(horizontal: 5.w),
                  child: TextField(
                    controller: phoneController,
                    style: TextStyle(color: Colors.white, fontSize: 14.sp),
                    keyboardType: TextInputType.phone,
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: '请输入手机号',
                      contentPadding: EdgeInsets.only(top: 10.w),
                      hintStyle: TextStyle(
                        color: Colours.color5C5C6E,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 15.w),

                // 地址输入框
                Container(
                  height: 54.w,
                  decoration: BoxDecoration(
                    color: Colours.color22222D,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  margin: EdgeInsets.symmetric(horizontal: 5.w),
                  child: TextField(
                    controller: addressController,
                    style: TextStyle(color: Colors.white, fontSize: 14.sp),
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: '请输入收货地址',
                      contentPadding: EdgeInsets.only(top: 10.w),
                      hintStyle: TextStyle(
                        color: Colours.color5C5C6E,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 20.w),

                // 发货说明
                Text(
                  "预计将在兑换后7个工作日内为您发货",
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colours.color5C5C6E,
                    fontWeight: FontWeight.bold,
                  ),
                ).paddingOnly(left: 8.w),
                SizedBox(height: 25.w),

                // 按钮区域
                Row(
                  children: [
                    // 取消按钮
                    Expanded(
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          AppPage.back();
                        },
                        child: Container(
                          height: 46.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Colours.color2C2C39,
                            borderRadius: BorderRadius.circular(28.r),
                          ),
                          child: Text(
                            "取消",
                            style: TextStyle(
                              fontSize: 15.sp,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 15.w),

                    // 兑换按钮
                    Expanded(
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          if (phoneController.text.isNotEmpty &&
                              addressController.text.isNotEmpty) {
                            onExchange(
                                phoneController.text, addressController.text);
                            AppPage.back();
                          }
                        },
                        child: Container(
                          height: 46.w,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(28.r),
                            gradient: GradientUtils.mainGradient,
                          ),
                          child: Text(
                            "兑换",
                            style: TextStyle(
                              fontSize: 15.sp,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    ),
    barrierColor: Colors.black.withOpacity(0.85),
    barrierDismissible: false,
  );
}
