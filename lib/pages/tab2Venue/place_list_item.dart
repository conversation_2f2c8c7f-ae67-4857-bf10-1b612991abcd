import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shoot_z/pages/tab3Create/place/models/place_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import '../../gen/assets.gen.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../generated/l10n.dart';

class PlaceListItem extends StatelessWidget {
  final PlaceModel model;
  const PlaceListItem({super.key, required this.model});
  static final desList = [
    "#${S.current.video_free}",
    S.current.match_reports_available
  ];
  @override
  Widget build(BuildContext context) {
    List<String> des = [];
    if (model.isBuy) {
      des.add(desList.first);
    }
    if (model.isReport) {
      des.add(desList.last);
    }

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => AppPage.to(Routes.arenaDetailsPage, arguments: {
        "id": model.arenaID,
      }),
      child: Padding(
        padding: const EdgeInsets.only(bottom: 15, left: 15, right: 15),
        child: Stack(children: [
          Container(
            padding:
                const EdgeInsets.only(top: 15, bottom: 19, left: 15, right: 15),
            decoration: BoxDecoration(
              color: Colours.color191921,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: Stack(children: [
                        CachedNetworkImage(
                          imageUrl: model.logo,
                          width: 97.w,
                          height: 97.w,
                          fit: BoxFit.cover,
                        ),
                        if (model.type > 0)
                          Positioned(
                              bottom: 0,
                              left: 0,
                              right: 0,
                              child: Container(
                                height: 28.w,
                                alignment: Alignment.center,
                                padding: EdgeInsets.only(top: 4.w),
                                decoration: BoxDecoration(
                                  image: DecorationImage(
                                      image: WxAssets.images.icHlItemBottom
                                          .provider(),
                                      fit: BoxFit.fill),
                                ),
                                child: Text(
                                  model.label,
                                  style: TextStyles.display10,
                                ),
                              )),
                      ]),
                    ),
                    Expanded(
                      child: Padding(
                        padding:
                            const EdgeInsets.only(left: 16, right: 16, top: 3),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              model.arenaName,
                              style: TextStyles.titleSemiBold16,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(
                              height: 10.w,
                            ),
                            Padding(
                              padding: EdgeInsets.only(bottom: 3.w),
                              child: Row(
                                children: [
                                  WxAssets.images.icTime
                                      .image(width: 14, height: 14),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  Text(
                                    '${model.beginTime.substring(0, 5)}～${model.endTime.substring(0, 5)}',
                                    style: TextStyles.display12,
                                  )
                                ],
                              ),
                            ),
                            if (model.floorCondition.isNotEmpty)
                              Padding(
                                padding: EdgeInsets.only(bottom: 3.w),
                                child: Row(
                                  children: [
                                    WxAssets.images.icGou
                                        .image(width: 14, height: 14),
                                    const SizedBox(
                                      width: 5,
                                    ),
                                    Expanded(
                                      child: Text(
                                        removeDotIfLast(model.floorCondition
                                            .replaceAll('@', '·')),
                                        style: TextStyles.display12,
                                        overflow: TextOverflow.ellipsis,
                                        maxLines: 1,
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            Row(
                              children: [
                                WxAssets.images.icLocation
                                    .image(width: 14, height: 14),
                                const SizedBox(
                                  width: 5,
                                ),
                                Text(
                                  "${model.distance}km",
                                  style: TextStyles.display12,
                                )
                              ],
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                Text(
                  des.join('，'),
                  style:
                      TextStyles.display12.copyWith(color: Colours.color9D4FEF),
                )
              ],
            ),
          ),
          Positioned(
            bottom: 15,
            right: 12,
            child: WxButton(
              text: S.current.entering_the_arena,
              textStyle: TextStyles.titleSemiBold16
                  .copyWith(fontSize: 12.w, color: Colours.color333333),
              width: 84.w,
              height: 32.w,
              backgroundColor: Colors.white,
              borderRadius: BorderRadius.circular(16.w),
              onPressed: () {
                log("message11=${model.arenaID}");
                // 进入球馆主页
                AppPage.to(Routes.arenaDetailsPage, arguments: {
                  "id": model.arenaID, // ??,

                  //  model.arenaID == 32
                  //     ? 11
                  //     : model.arenaID == 46
                  //         ? 57
                  //         : model.arenaID
                });
              },
            ),
          ),
        ]),
      ),
    );
  }

  String removeDotIfLast(String input) {
    if (input.endsWith('·')) {
      return input.substring(0, input.length - 1);
    }
    return input;
  }
}
