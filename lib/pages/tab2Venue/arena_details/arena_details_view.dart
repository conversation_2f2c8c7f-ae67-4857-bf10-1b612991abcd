import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_swiper_plus/flutter_swiper_plus.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/game/game_list_item.dart';
import 'package:shoot_z/pages/game/models/game_model.dart';
import 'package:shoot_z/pages/tab2Venue/arena_details/arena_details_logic.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/RectIndicator.dart';
import 'package:shoot_z/widgets/keep_alive_widget.dart';
import 'package:shoot_z/widgets/video/video_view.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///球场主页详情 二级页面
class ArenaDetailsPage extends StatelessWidget {
  ArenaDetailsPage({super.key});

  final logic = Get.put(ArenaDetailsLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar:  MyAppBar(
      //   title: Text("${logic.dataFag["isFrist"]}"),
      // ),
      body: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? SafeArea(
                bottom: false,
                child: Stack(
                  children: [
                    buildLoad(),
                    Positioned(
                        left: 17.w,
                        top: 14.w,
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            Get.back();
                          },
                          child: WxAssets.images.arrowLeft.image(
                              color: Colors.white, width: 40.w, height: 40.w),
                        ))
                  ],
                ),
              )
            : KeepAliveWidget(
                child: Container(
                  width: double.infinity,
                  child: SafeArea(
                    bottom: false,
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _videoWidget(),
                          Expanded(
                            child: Stack(
                              children: [
                                SingleChildScrollView(
                                  child: Container(
                                    width: double.infinity,
                                    child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          //球馆详情页面
                                          InfoWidget(context),
                                          //球馆比赛列表
                                          _listWidget(context),
                                          const SizedBox(
                                            height: 20,
                                          ),
                                        ]),
                                  ),
                                ),
                                Positioned(
                                  top: logic.offset.value.dy,
                                  left: logic.offset.value.dx,
                                  child: Visibility(
                                    visible: !logic.isDaka.value,
                                    child: GestureDetector(
                                      onPanUpdate: (detail) {
                                        logic.offset.value = logic.calOffset(
                                            MediaQuery.of(context).size,
                                            logic.offset.value,
                                            detail.delta);
                                      },
                                      onPanEnd: (detail) {},
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () {
                                        if (UserManager.instance.isLogin) {
                                          if (logic.isDaka.value) {
                                            //已经打卡
                                            WxLoading.showToast(
                                                S.current.already_clocked);
                                          } else {
                                            //打卡弹窗
                                            logic.getDaka();
                                            //logic.getPointsIsClockIn();
                                          }
                                        } else {
                                          // WxLoading.showToast("请先登录");
                                          AppPage.to(Routes.login)
                                              .then((onValue) async {
                                            await Future.delayed(const Duration(
                                                milliseconds: 500));
                                            log("getPointsIsClockIn=$onValue-${UserManager.instance.isLogin}");
                                            if (UserManager.instance.isLogin) {
                                              await logic.getPointsIsClockIn();
                                              if (logic.isDaka.value) {
                                                //已经打卡
                                                WxLoading.showToast(
                                                    S.current.already_clocked);
                                              } else {
                                                //打卡弹窗
                                                logic.getDaka();
                                                //logic.getPointsIsClockIn();
                                              }
                                            } else {
                                              log("getPointsIsClockIn2=$onValue-${UserManager.instance.isLogin}");
                                              WxLoading.showToast(
                                                  S.current.please_login);
                                            }
                                          });
                                        }
                                      },
                                      child: WxAssets.images.daka1
                                          .image(width: 72.w, height: 58.w),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          )
                        ]),
                  ),
                ),
              );
      }),
      bottomNavigationBar: Obx(() {
        return (logic.dataFag["isFrist"] as bool)
            ? SizedBox()
            : Container(
                width: double.infinity,
                padding: EdgeInsets.only(
                    bottom: 34.w, left: 15.w, right: 15.w, top: 1.w),
                child: Row(
                  children: [
                    if (logic.dataCheckList.isNotEmpty)
                      GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () async {
                          if (await Utils.isToLogin()) {
                            //素材库
                            AppPage.to(Routes.materialLibraryPage, arguments: {
                              "arenaID": logic.arenaID.value,
                            }).then((v) {
                              if (UserManager.instance.isLogin) {
                                logic.getDaoGoalList();
                              }
                            });
                          }
                        },
                        child: Stack(
                          children: [
                            Container(
                              width: 44.w,
                              height: 44.w,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(16.r),
                                  color: Colours.color282735),
                              child: WxAssets.images.optionDocument
                                  .image(width: 24.w, height: 24.w),
                            ),
                            if (logic.dataCheckList.isNotEmpty)
                              Positioned(
                                right: 0,
                                child: Transform.translate(
                                  offset: const Offset(5, 0), // 移动50像素到右和下
                                  child: Container(
                                    padding: EdgeInsets.only(
                                        left: 5.w,
                                        right: 5.w,
                                        top: 1.w,
                                        bottom: 1.w),
                                    decoration: BoxDecoration(
                                      color: Colours.red,
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(28.r)),
                                    ),
                                    child: Text(
                                      "${logic.dataCheckList.length}",
                                      style: TextStyles.titleMedium18
                                          .copyWith(fontSize: 10.sp),
                                    ),
                                  ),
                                ),
                              )
                          ],
                        ),
                      ),
                    if (logic.dataCheckList.isNotEmpty)
                      SizedBox(
                        width: 10.w,
                      ),
                    Expanded(
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          logic.getDataInfo(true);
                          //isPremium	integer 是否为高级球馆
                          //AppPage.to(Routes.optionGoalPage);
                          if (logic.arenaDetailsModel.value.isPremium == 1) {
                            //选择场地
                            AppPage.to(Routes.optionSitePage, arguments: {
                              "arenaID": logic.arenaID.value,
                              "type": 1
                            }).then((v) {
                              if (UserManager.instance.isLogin) {
                                logic.getDaoGoalList();
                              }
                            });
                          } else {
                            //选择场地
                            AppPage.to(Routes.optionSitePage, arguments: {
                              "arenaID": logic.arenaID.value,
                              "type": 0
                            }).then((v) {
                              if (UserManager.instance.isLogin) {
                                logic.getDaoGoalList();
                              }
                            });
                          }
                        },
                        child: Container(
                          height: 46.w,
                          width: double.infinity,
                          alignment: Alignment.center,
                          padding: EdgeInsets.only(
                              left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                          decoration: BoxDecoration(
                            color: Colours.color282735,
                            borderRadius:
                                BorderRadius.all(Radius.circular(28.r)),
                            gradient: const LinearGradient(
                              colors: [
                                Colours.color7732ED,
                                Colours.colorA555EF
                              ],
                              begin: Alignment.bottomLeft,
                              end: Alignment.bottomRight,
                            ),
                          ),
                          child: logic.arenaDetailsModel.value.isPremium == 1
                              ? Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    WxAssets.images.ai.image(
                                        width: 35.w,
                                        height: 35.w,
                                        color: Colours.white),
                                    SizedBox(
                                      width: 2.w,
                                    ),
                                    Text(
                                      S.current.get_my_highlights,
                                      style: TextStyles.titleMedium18
                                          .copyWith(fontSize: 16.sp),
                                    ),
                                  ],
                                )
                              : Text(
                                  S.current.get_my_video,
                                  style: TextStyles.titleMedium18
                                      .copyWith(fontSize: 16.sp),
                                ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
      }),
    );
  }

  Widget _videoWidget() {
    return Obx(() {
      return Container(
        width: double.infinity,
        height: ScreenUtil().screenWidth / 375 * 211,
        child: Stack(
          children: [
            Swiper(
              itemBuilder: (context, position) {
                return logic.arenaDetailsModel.value.path?[position]?.type == 0
                    ? MyImage(
                        logic.arenaDetailsModel.value.path?[position]?.path ??
                            '',
                        fit: BoxFit.fill,
                        width: double.infinity,
                        height: ScreenUtil().screenWidth / 375 * 211,
                        errorImage: "error_image_width.png")
                    : AspectRatio(
                        aspectRatio: 375 / 211, // 宽高比
                        child: VideoView(
                          controller: logic.videoController,
                        ),
                      );
              },
              itemCount: logic.arenaDetailsModel.value.path?.length ?? 0,
              autoplay: false,
              loop: false,
              controller: logic.swiperController,
              // pagination: SwiperPagination( alignment: Alignment.bottomRight),
              pagination: SwiperPagination(
                alignment: Alignment.bottomRight,
                builder: SwiperCustomPagination(
                    builder: (BuildContext context, SwiperPluginConfig config) {
                  return RectIndicator(
                      alignment: Alignment.bottomRight,
                      position: config.activeIndex,
                      count: logic.arenaDetailsModel.value.path?.length ?? 0,
                      color: Colors.white.withOpacity(1),
                      activeColor: const Color(0xFFD8D8D8),
                      //未选中 指示器颜色，选中的颜色key为Color
                      width: 7.0,
                      //指示器宽度
                      activeWidth: 6.0,
                      //选中的指示器宽度
                      radius: 2,
                      //指示器圆角角度
                      height: 4.0);
                }),
              ),
              onTap: (position) async {
                // if (await canLaunch(ads[position].url.toString())) {
                //   launch(ads[position].url.toString());
                // }
              },
              onIndexChanged: (value) {
                logic.changeSwiper(value);
              },
            ),
            Positioned(
                left: 17.w,
                top: 14.w,
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    Get.back();
                  },
                  child: WxAssets.images.arrowLeft
                      .image(color: Colors.white, width: 40.w, height: 40.w),
                ))
          ],
        ),
      );
    });
  }

  Container InfoWidget(BuildContext context) {
    return Container(
      width: double.infinity,
      padding:
          EdgeInsets.only(left: 15.w, right: 15.w, top: 20.w, bottom: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            logic.arenaDetailsModel.value.arenaName ?? "",
            style: TextStyles.titleSemiBold16.copyWith(fontSize: 22.sp),
          ),
          SizedBox(
            height: 12.w,
          ),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Wrap(
                      spacing: 5.w,
                      runSpacing: 5.w,
                      children: List.generate(
                          logic.arenaDetailsModel.value.floorCondition
                                  ?.length ??
                              0, (index) {
                        return Container(
                          padding: EdgeInsets.only(
                              left: 4.w, right: 4.w, top: 1.w, bottom: 1.3.w),
                          decoration: BoxDecoration(
                              color: Colours.color282735,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(2.r))),
                          child: Text(
                            logic.arenaDetailsModel.value
                                    .floorCondition?[index] ??
                                "",
                            style: TextStyle(
                                color: Colours.color9393A5, fontSize: 12.sp),
                          ),
                        );
                      }),
                    ),
                    SizedBox(
                      height: 12.w,
                    ),
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.only(
                              left: 5.w, right: 5.w, top: 3.w, bottom: 3.w),
                          decoration: BoxDecoration(
                            color: Colours.color282735,
                            borderRadius:
                                BorderRadius.all(Radius.circular(2.r)),
                            gradient: const LinearGradient(
                              colors: [
                                Colours.color7732ED,
                                Colours.colorA555EF
                              ],
                              begin: Alignment.bottomLeft,
                              end: Alignment.bottomRight,
                            ),
                          ),
                          child: Text(
                            S.current.capture_time,
                            style: TextStyles.titleMedium18
                                .copyWith(fontSize: 11.sp),
                          ),
                        ),
                        SizedBox(
                          width: 6.w,
                        ),
                        Text(
                          "${(logic.arenaDetailsModel.value.beginTime ?? "").length >= 5 ? (logic.arenaDetailsModel.value.beginTime ?? "").substring(0, 5) : (logic.arenaDetailsModel.value.beginTime ?? "")}-${(logic.arenaDetailsModel.value.endTime ?? "").length >= 5 ? (logic.arenaDetailsModel.value.endTime ?? "").substring(0, 5) : (logic.arenaDetailsModel.value.endTime ?? "")}",
                          style: TextStyles.titleMedium18
                              .copyWith(fontSize: 14.sp),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 12.w,
                    ),
                  ],
                ),
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  MyShareH5.getShareH5(ShareArenaDetails(
                      arenaId: logic.arenaID.value.toString()));
                },
                child: Container(
                  width: 40.w,
                  height: 40.w,
                  alignment: Alignment.centerRight,
                  child: WxAssets.images.share3
                      .image(color: Colors.white, width: 22.w, height: 22.w),
                ),
              ),
              SizedBox(
                width: 18.w,
              ),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  log("openMapsSheet2-${logic.arenaDetailsModel.value.tel ?? ""}");
                  if (logic.arenaDetailsModel.value.tel!.isNotEmpty) {
                    log("openMapsSheet3-${logic.arenaDetailsModel.value.tel ?? ""}");
                    Get.dialog(
                      CustomAlertDialog(
                        title: S.current.call_phone(
                            logic.arenaDetailsModel.value.tel ?? ""),
                        onPressed: () {
                          AppPage.back();
                          Utils.phoneTelURL(
                              logic.arenaDetailsModel.value.tel ?? "");
                        },
                      ),
                    );
                  } else {
                    WxLoading.showToast(S.current.no_number_club);
                  }
                },
                child: Container(
                  width: 30.w,
                  height: 40.w,
                  child: WxAssets.images.mobile3
                      .image(color: Colors.white, width: 22.w, height: 22.w),
                ),
              )
            ],
          ),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              //  log("openMapsSheet2");
              if (logic.arenaDetailsModel.value.latitude! > 0 ||
                  logic.arenaDetailsModel.value.longitude! > 0) {
                logic.openMapsSheet(
                    context,
                    //  "湖南省长沙市望城区春晖花园小区", //
                    logic.arenaDetailsModel.value.address ?? "",
                    logic.arenaDetailsModel.value.latitude ?? 0.0,
                    logic.arenaDetailsModel.value.longitude ?? 0.0);
              } else {
                WxLoading.showToast(S.current.no_location_club);
              }
            },
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                WxAssets.images.icLocation
                    .image(color: Colors.white, width: 15.w, height: 15.w),
                SizedBox(
                  width: 3.w,
                ),
                Expanded(
                  child: Text(
                    logic.arenaDetailsModel.value.address ?? "",
                    style:
                        TextStyle(color: Colours.color9393A5, fontSize: 12.sp),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                Text(
                  S.current.distance_you(
                      logic.arenaDetailsModel.value.distance ?? ""),
                  style: TextStyle(color: Colours.color9393A5, fontSize: 12.sp),
                ),
                WxAssets.images.icArrowRight
                    .image(color: Colors.white, width: 12.w, height: 12.w)
              ],
            ),
          ),
          SizedBox(
            height: 12.w,
          ),
          const Divider(
            color: Color(0xff191921),
            thickness: 1,
            height: 1,
          ),
          SizedBox(
            height: 20.w,
          ),
          Text(
            S.current.recent_games,
            style: TextStyles.titleSemiBold16.copyWith(fontSize: 16.sp),
          ),
        ],
      ),
    );
  }

  /// 列表数据
  _listWidget(BuildContext context) {
    return Obx(() {
      return

          // SmartRefresher(
          //   controller: logic.refreshController,
          //   footer: buildFooter(),
          //   header: buildClassicHeader(),
          //   enablePullDown: true,
          //   enablePullUp: logic.dataList.isNotEmpty,
          //   onRefresh: () {
          //     logic.getdataList(
          //         isLoad: false, controller: logic.refreshController);
          //   },
          //   onLoading: () {
          //     logic.getdataList(controller: logic.refreshController);
          //   },
          //   // physics: const AlwaysScrollableScrollPhysics(),
          //   physics: const NeverScrollableScrollPhysics(),
          //   child:

          (logic.dataFag["isFrist"] as bool)
              ? buildLoad()
              : logic.dataList.isEmpty
                  ? SizedBox(
                      height: 300.w,
                      child: myNoDataView(
                        context,
                        msg: S.current.no_matches_yet,
                        imagewidget: WxAssets.images.icGameNo
                            .image(width: 150.w, height: 150.w),
                      ))
                  : ListView.builder(
                      scrollDirection: Axis.vertical,
                      // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                      shrinkWrap: true,
                      padding: EdgeInsets.zero,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: logic.dataList.length,
                      itemBuilder: (context, position) {
                        return _listItemWidget(logic.dataList[position]);
                      });
      // );
    });
  }

  /// 构建列表项
  Widget _listItemWidget(Matches item) {
    return Container(
      margin: EdgeInsets.only(left: 15.w, right: 15.w, bottom: 15.w),
      child: GameListItem(
        item: item,
        type: 2,
        tap: (v) {},
      ),
    );
  }
}
