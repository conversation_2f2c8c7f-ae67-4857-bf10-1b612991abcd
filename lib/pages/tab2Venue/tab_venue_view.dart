import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import '../../generated/l10n.dart';
import '../../utils/location_utils.dart';
import '../../widgets/view.dart';
import 'tab_venue_logic.dart';
import 'place_list_item.dart';

class TabVenuePage extends StatefulWidget {
  const TabVenuePage({super.key});

  @override
  State<TabVenuePage> createState() => _HomePageState();
}

class _HomePageState extends State<TabVenuePage>
    with AutomaticKeepAliveClientMixin {
  final logic = Get.put(TabVenueLogic());
  final state = Get.find<TabVenueLogic>().state;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      appBar: MyAppBar(
        title: Text(S.current.site1),
        leading: const SizedBox(),
      ),
      body: Obx(() => state.init.value
          ? RefreshIndicator(
              onRefresh: logic.onRefresh,
              child: Builder(builder: (context) {
                return Obx(
                  () => CustomScrollView(
                    slivers: [
                      SliverToBoxAdapter(
                        child: SizedBox(
                          height: 37,
                          width: Get.width,
                          child: Stack(
                            children: [
                              Positioned(
                                  left: 0,
                                  top: 9,
                                  child: WxAssets.images.homeTitleBg
                                      .image(width: 75, fit: BoxFit.fill)),
                              Positioned(
                                  left: 17,
                                  top: 0,
                                  right: 15,
                                  child: Row(children: [
                                    // Text(
                                    //   S.current.recommended_stadium,
                                    //   style: TextStyles.titleBold22
                                    //       .copyWith(fontSize: 24),
                                    // ),
                                    WxAssets.images.icHomeTj
                                        .image(width: 88.w, fit: BoxFit.fill),
                                    const Spacer(),
                                    GestureDetector(
                                      onTap: () => logic.toAll(),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            S.current.all,
                                            style: TextStyles.display12
                                                .copyWith(
                                                    color: Colours.color9393A5),
                                          ),
                                          const SizedBox(
                                            width: 2,
                                          ),
                                          WxAssets.images.icArrowRight
                                              .image(width: 14, height: 14)
                                        ],
                                      ),
                                    )
                                  ])),
                            ],
                          ),
                        ),
                      ),
                      LocationUtils.instance.havePermission.value
                          ? _list()
                          : _location(),
                    ],
                  ),
                );
              }),
            )
          : buildLoad()),
    );
  }

  SliverToBoxAdapter _location() {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          SizedBox(
            height: 71.w,
          ),
          WxAssets.images.icHomeLocationHint
              .image(width: 104.w, fit: BoxFit.fill),
          SizedBox(
            height: 30.w,
          ),
          Text(
            S.current.location_tips,
            style: TextStyles.regular.copyWith(color: Colours.color5C5C6E),
          ),
          SizedBox(
            height: 30.w,
          ),
          WxButton(
            width: 125.w,
            height: 40.w,
            borderRadius: BorderRadius.circular(20.w),
            backgroundColor: Colours.color22222D,
            text: S.current.open_now,
            textStyle: TextStyles.regular,
            onPressed: logic.openSettings,
          ),
        ],
      ),
    );
  }

  SliverList _list() {
    return SliverList(
        delegate: SliverChildBuilderDelegate(
      (BuildContext context, int index) {
        return index < state.list.length
            ? PlaceListItem(
                model: state.list[index],
              )
            : allPlace();
      },
      childCount: state.list.isNotEmpty ? state.list.length + 1 : 0,
    ));
  }

  Widget allPlace() {
    return Padding(
        padding: const EdgeInsets.only(bottom: 30, top: 5),
        child: Center(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            height: 30,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              border: Border.all(color: Colours.color2F2F3B, width: 1),
            ),
            child: GestureDetector(
              onTap: () => logic.toAll(),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  WxAssets.images.icMoreAll.image(),
                  const SizedBox(
                    width: 4,
                  ),
                  Text(
                    S.current.all_arenas,
                    style: TextStyles.display12.copyWith(color: Colors.white),
                  )
                ],
              ),
            ),
          ),
        ));
  }

  @override
  bool get wantKeepAlive => true;
}
