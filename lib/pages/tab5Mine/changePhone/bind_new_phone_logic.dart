import 'dart:async';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_env.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/login/encrypt.dart';
import 'package:shoot_z/pages/login/models/user_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:ui_packages/ui_packages.dart';

class BindNewPhoneLogic extends GetxController {
//  什么时候调用 onClose：
//  •	控制器的依赖被释放时触发，例如：
//  •	页面销毁且没有其他地方依赖该控制器。
//  •	显式调用 Get.delete 删除控制器。
  TextEditingController phoneController = TextEditingController();
  TextEditingController codeController = TextEditingController();

  // 倒计时相关状态
  var countdown = 0.obs;
  var isCountdownActive = false.obs;
  Timer? countdownTimer;
  @override
  void onClose() {
    // 清理定时器
    countdownTimer?.cancel();
    super.onClose();
  }

  /// 开始倒计时
  void startCountdown() {
    countdown.value = 60;
    isCountdownActive.value = true;

    countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (countdown.value > 0) {
        countdown.value--;
      } else {
        timer.cancel();
        isCountdownActive.value = false;
      }
    });
  }

  void getCode(BuildContext context) async {
    if (phoneController.text.isEmpty ||
        !Utils.isValidPhoneNumber(phoneController.text)) {
      WxLoading.showToast(S.current.mobile_number_hint);
      return;
    }
    onKeyDismiss();
  }

  void sureAction(BuildContext context) async {
    if (codeController.text.length != 4) {
      WxLoading.showToast('请输入有效的验证码');
      return;
    }
    onKeyDismiss();
    showAgreeSheet(context);
  }

  void showAgreeSheet(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return Dialog(
          backgroundColor: Colours.color191921,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.r),
          ),
          insetPadding: EdgeInsets.symmetric(horizontal: 35.w),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 30.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text('${S.current.old_phone_number}：13831323324',
                    style: TextStyles.titleSemiBold16
                        .copyWith(color: Colours.colorA8A8BC)),
                SizedBox(
                  height: 20.w,
                ),
                Text('${S.current.new_phone_number}：13831323324',
                    style: TextStyles.titleSemiBold16),
                SizedBox(
                  height: 20.w,
                ),
                Text(S.current.bind_sure_tips,
                    textAlign: TextAlign.center,
                    style: TextStyles.regular
                        .copyWith(color: Colours.colorA8A8BC, height: 1.71)),
                SizedBox(height: 30.w),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    WxButton(
                      text: S.current.cancel,
                      textStyle: TextStyles.semiBold14,
                      height: 40.w,
                      width: 112.w,
                      borderSide: BorderSide(color: Colours.white, width: 1.w),
                      backgroundColor: Colours.color191921,
                      borderRadius: BorderRadius.circular(23.w),
                      onPressed: () => AppPage.back(),
                    ),
                    WxButton(
                      text: S.current.sure,
                      textStyle: TextStyles.semiBold14,
                      height: 40.w,
                      width: 112.w,
                      borderRadius: BorderRadius.circular(23.w),
                      linearGradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                      onPressed: () async {},
                    ),
                  ],
                ).marginSymmetric(horizontal: 30.w)
              ],
            ),
          ),
        );
      },
    );
  }
}
