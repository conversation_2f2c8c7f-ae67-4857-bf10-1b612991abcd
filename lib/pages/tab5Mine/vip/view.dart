import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/pages/tab5Mine/vip/logic.dart';
import 'package:shoot_z/pages/tab5Mine/vip/state.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:ui_packages/ui_packages.dart';

import '../../../gen/assets.gen.dart';
import '../../../generated/l10n.dart';

class VipPage extends StatefulWidget {
  const VipPage({super.key});

  @override
  State<VipPage> createState() => _VipPageState();
}

class _VipPageState extends State<VipPage> {
  final logic = Get.put(VipLogic());
  final state = Get.find<VipLogic>().state;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colours.bg_color,
      child: Stack(
        children: [
          SingleChildScrollView(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).padding.bottom + 10.w),
            child: Obx(
              () => Column(
                children: [
                  _top(context),
                  Obx(() => Visibility(
                      visible: !state.switchVip.value,
                      child: _ssqHintView(context).marginOnly(bottom: 20.w))),
                  _hintView(context),
                  SizedBox(
                    height: 20.w,
                  ),
                  _vipPriceList(context),
                  if (state.switchVip.value) _selPlace(context),
                  SizedBox(
                    height: 25.w,
                  ),
                  _okWidget(context),
                  SizedBox(
                    height: 35.w,
                  ),
                  _vipContent(context),
                ],
              ),
            ),
          ),
          _title(context),
        ],
      ),
    );
  }

  Widget _title(BuildContext context) {
    return Positioned(
        left: 0,
        right: 0,
        top: 0,
        child: SizedBox(
          width: Get.width,
          height: kToolbarHeight + MediaQuery.of(context).padding.top,
          child: Stack(
            children: [
              Opacity(
                opacity: 1,
                child: Container(
                  decoration:
                      BoxDecoration(gradient: GradientUtils.mainGradient),
                ),
              ),
              Positioned(
                  top: MediaQuery.of(context).padding.top,
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Stack(children: [
                    Positioned(
                      top: 0,
                      bottom: 0,
                      child: GestureDetector(
                        onTap: () => AppPage.back(),
                        child: WxAssets.images.arrowLeft
                            .image(color: Colors.white),
                      ),
                    ),
                    Center(
                      child: Text(
                        S.current.membership_benefits,
                        style: TextStyles.display16,
                      ),
                    ),
                  ])),
            ],
          ),
        ));
  }

  Widget _top(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).padding.top + kToolbarHeight + 99.w + 90.w,
      child: Stack(
        children: [
          Container(
            width: Get.width,
            height: 225.w,
            decoration: BoxDecoration(
              gradient: GradientUtils.mainGradient,
            ),
          ),
          Positioned(
              top: MediaQuery.of(context).padding.top + 41.w,
              right: 11.w,
              child:
                  WxAssets.images.icVipLogo.image(width: 140.w, height: 140.w)),
          Positioned(
              top: MediaQuery.of(context).padding.top + kToolbarHeight + 21.w,
              left: 17.w,
              child: Row(
                children: [
                  Container(
                    width: 52,
                    height: 52,
                    decoration: BoxDecoration(
                        border: Border.all(color: Colours.white, width: 1),
                        shape: BoxShape.circle,
                        image: DecorationImage(
                            image: CachedNetworkImageProvider(
                                UserManager.instance.userInfo.value?.avatar ??
                                    ''),
                            fit: BoxFit.cover)),
                  ),
                  SizedBox(
                    width: 11.w,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        UserManager.instance.userInfo.value?.userName ?? '',
                        style: TextStyles.titleSemiBold16,
                      ),
                      SizedBox(
                        height: 5.w,
                      ),
                      Obx(() {
                        var text = '';
                        final user = UserManager.instance.userInfo.value;
                        if (user != null) {
                          text = user.vipExpiredFlag == -1
                              ? S.current.membership_not_yet_activated
                              : (user.vipExpiredFlag == 0
                                  ? S.current
                                      .vip_expire(user.vipExpireDate as String)
                                  : S.current
                                      .vip_expired(user.vipExpiredDays as int));
                        }
                        return Text(
                          text,
                          style: TextStyles.display12
                              .copyWith(color: Colours.white),
                        );
                      }),
                    ],
                  ),
                ],
              )),
          Positioned(
              top: MediaQuery.of(context).padding.top + kToolbarHeight + 99.w,
              left: 0,
              right: 0,
              child: Stack(
                children: [
                  Positioned(
                      left: 0,
                      right: 0,
                      top: 10.w,
                      bottom: 0,
                      child: Container(
                        decoration: const BoxDecoration(
                          color: Colours.color1F1F29,
                          borderRadius:
                              BorderRadius.vertical(top: Radius.circular(20)),
                        ),
                      )),
                  Obx(
                    () => Transform(
                        transform: Matrix4.identity()
                          ..scale(state.switchVip.value ? -1.0 : 1.0, 1.0),
                        // 水平方向翻转,
                        alignment: Alignment.center,
                        child: WxAssets.images.icVipSvipSel.image(
                            width: Get.width, height: 90.w, fit: BoxFit.fill)),
                  ),
                  Positioned(
                    left: 0,
                    right: 0,
                    top: 21.w,
                    bottom: 30.w,
                    child: Row(
                      children: [
                        Expanded(
                            child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () => logic.switchVip(false),
                          child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                WxAssets.images.icVipSvip
                                    .image(width: 56.w, height: 20.w),
                                Text(
                                  S.current.all_stadiums_available,
                                  style: TextStyles.display10
                                      .copyWith(color: Colours.color5C5C6E),
                                )
                              ]),
                        )),
                        Expanded(
                            child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () => logic.switchVip(true),
                          child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                WxAssets.images.icVipVip
                                    .image(width: 56.w, height: 20.w),
                                Text(
                                  "指定球馆可用",
                                  style: TextStyles.display10
                                      .copyWith(color: Colours.color5C5C6E),
                                )
                              ]),
                        )),
                      ],
                    ),
                  ),
                ],
              )),
        ],
      ),
    );
  }

  Widget _hintView(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Container(
        height: 28.w,
        decoration: BoxDecoration(
          image: DecorationImage(
              image: WxAssets.images.icVipHintBg.provider(), fit: BoxFit.fill),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 34.w,
            ),
            Obx(
              () => Text.rich(TextSpan(
                children: [
                  TextSpan(text: "开通得", style: TextStyles.display10),
                  TextSpan(
                      text: '${logic.price?.point ?? 0}',
                      style: TextStyles.display10.copyWith(
                          fontWeight: AppFontWeight.semiBold(),
                          color: Colours.colorFFD60A)),
                  TextSpan(
                      text: "积分，积分可在积分商城兑换会员、运动装备等",
                      style: TextStyles.display10),
                ],
              )),
            ),
          ],
        ),
      ),
    );
  }

  Widget _vipPriceList(BuildContext context) {
    return SizedBox(
      height: 132.w,
      child: Obx(() {
        final list = state.switchVip.value
            ? state.vipPriceModel.value?.vip
            : state.vipPriceModel.value?.svip;
        return ListView.builder(
            padding: EdgeInsets.only(left: 15.w, right: 2.w),
            scrollDirection: Axis.horizontal,
            itemCount: list?.length ?? 0,
            itemBuilder: (context, index) {
              return _listItem(context, index);
            });
      }),
    );
  }

  Widget _listItem(BuildContext context, int index) {
    final list = state.switchVip.value
        ? state.vipPriceModel.value!.vip
        : state.vipPriceModel.value!.svip;
    final model = list[index];
    final isNew = model.userRecommend;
    final des = isNew ? '¥${model.price}' : model.description;
    final desStyle =
        TextStyles.display14.copyWith(color: Colours.color5C5C6E, height: 1);
    final selIndex =
        state.switchVip.value ? state.selVipIndex : state.selSVipIndex;
    final priceColor = isNew ? Colours.color964AEE : Colours.white;
    return GestureDetector(
      onTap: () => selIndex.value = index,
      child: Stack(children: [
        Obx(
          () {
            final curr = selIndex.value == index;
            final nameColor = curr
                ? (isNew ? Colors.black : Colors.white)
                : Colours.color9393A5;
            final image = curr
                ? (isNew ? WxAssets.images.icVipNew : WxAssets.images.icVipSel)
                : WxAssets.images.icVipNormal;
            return Container(
              margin: EdgeInsets.only(top: 10.w, right: 13.w),
              padding: EdgeInsets.only(left: 12.w),
              width: 102.w,
              decoration: BoxDecoration(
                image:
                    DecorationImage(image: image.provider(), fit: BoxFit.fill),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 20.w,
                  ),
                  Text(
                    model.name,
                    style: TextStyles.titleSemiBold16
                        .copyWith(color: nameColor, height: 1),
                  ),
                  SizedBox(
                    height: 10.w,
                  ),
                  Text(
                    des,
                    style: !isNew
                        ? desStyle
                        : desStyle.copyWith(
                            decoration: TextDecoration.lineThrough, // 添加中划线
                            decorationColor: Colours.color5C5C6E, // 设置中划线颜色
                            decorationThickness: 1.0, // 设置中划线粗细
                            decorationStyle:
                                TextDecorationStyle.solid, // 设置中划线样式
                          ),
                  ),
                  SizedBox(
                    height: 19.w,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Padding(
                          padding: EdgeInsets.only(bottom: 3.w),
                          child: Text(
                            '¥',
                            style: TextStyles.display14.copyWith(
                                fontWeight: AppFontWeight.semiBold(),
                                height: 1),
                          )),
                      SizedBox(
                        width: 3.w,
                      ),
                      Text(model.discountPrice,
                          style: TextStyles.titleMedium18.copyWith(
                              fontSize: 28.w, height: 1, color: priceColor)),
                    ],
                  ),
                  // SizedBox(height: 9.w,),
                ],
              ),
            );
          },
        ),
        if (isNew)
          WxAssets.images.icVipNewPrice
              .image(width: 60.w, height: 17.w, fit: BoxFit.fill),
      ]),
    );
  }

  Widget _okWidget(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: logic.pay,
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 20.w),
            height: 55.w,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              gradient: GradientUtils.mainGradient,
              borderRadius: BorderRadius.circular(27.5.w),
            ),
            child: Obx(() {
              final open = UserManager.instance.userInfo.value?.vipLevel == 0
                  ? '开通'
                  : '续费';
              return Text.rich(TextSpan(
                children: [
                  TextSpan(
                      text: "确认协议并立即以",
                      style: TextStyles.titleSemiBold16.copyWith(height: 1)),
                  TextSpan(
                      text: logic.price?.discountPrice ?? '',
                      style: TextStyles.titleSemiBold16
                          .copyWith(fontSize: 22.sp, height: 1)),
                  TextSpan(
                      text: "元$open",
                      style: TextStyles.titleSemiBold16.copyWith(height: 1)),
                ],
              ));
            }),
          ),
        ),
        SizedBox(
          height: 15.w,
        ),
        Text.rich(TextSpan(children: [
          TextSpan(text: "我已确认", style: TextStyles.display12),
          TextSpan(
            text: "《会员服务协议》",
            style: TextStyles.display12.copyWith(
              color: Colors.white,
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () => logic.didVipPolicy(),
          ),
          TextSpan(text: "与", style: TextStyles.display12),
          TextSpan(
            text: "《隐私政策》",
            style: TextStyles.display12.copyWith(
              color: Colors.white,
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () => logic.didPrivacyPolicy(),
          ),
        ])),
      ],
    );
  }

  Widget _vipContent(BuildContext context) {
    return Column(
      children: [
        WxAssets.images.icVipTitle
            .image(width: 187.w, height: 23.w, fit: BoxFit.fill),
        SizedBox(
          height: 25.w,
        ),
        Obx(
          () => GridView.builder(
              padding: EdgeInsets.only(left: 25.w, right: 20.w),
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4, // 每行两个 item
                crossAxisSpacing: 20.w,
                mainAxisSpacing: 22.w,
                childAspectRatio: 67 / 92, // 控制每个 item 的宽高比例
              ),
              itemCount: state.switchVip.value
                  ? VipState.iconList2.length - 1
                  : VipState.iconList.length,
              itemBuilder: (context, itemIndex) {
                return Stack(children: [
                  Column(
                    children: [
                      state.switchVip.value
                          ? AssetGenImage(
                                  'assets/images/ic_vip_${VipState.iconList2[itemIndex]}.png')
                              .image(width: 62.w, fit: BoxFit.fill)
                          : AssetGenImage(
                                  'assets/images/ic_vip_${VipState.iconList[itemIndex]}.png')
                              .image(width: 62.w, fit: BoxFit.fill),
                      SizedBox(
                        height: 15.w,
                      ),
                      state.switchVip.value
                          ? Text(
                              VipState.textList2[itemIndex],
                              style:
                                  TextStyles.regular.copyWith(fontSize: 12.sp),
                            )
                          : Text(
                              VipState.textList[itemIndex],
                              style:
                                  TextStyles.regular.copyWith(fontSize: 12.sp),
                            )
                    ],
                  ),
                  Visibility(
                    visible: itemIndex == 0 && !state.switchVip.value,
                    child: Positioned(
                        right: 0,
                        top: 0,
                        child: WxAssets.images.icVipSsqTag
                            .image(width: 20.w, fit: BoxFit.fill)),
                  ),
                ]);
              }).marginOnly(bottom: 25.w),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                  padding: const EdgeInsets.only(top: 2),
                  child: WxAssets.images.tips
                      .image(width: 12.w, fit: BoxFit.fill)),
              SizedBox(
                width: 4.w,
              ),
              Expanded(
                  child: Text(
                S.current.vip_limit,
                style: TextStyles.regular.copyWith(
                    fontSize: 10.sp, color: Colours.color5C5C6E, height: 1.5),
              )),
            ],
          ),
        ),
      ],
    );
  }

  Widget _ssqHintView(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Container(
        height: 36.w,
        decoration: BoxDecoration(
          image: DecorationImage(
              image: WxAssets.images.icVipHintBg.provider(), fit: BoxFit.fill),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            WxAssets.images.icVipHint.image(width: 12.w, height: 12.w),
            SizedBox(
              width: 5.w,
            ),
            Text.rich(TextSpan(
              children: [
                TextSpan(text: "自开通日起，每30天可获得", style: TextStyles.display10),
                TextSpan(
                    text: '4',
                    style: TextStyles.display10.copyWith(
                        fontWeight: AppFontWeight.semiBold(),
                        color: Colours.colorFFF280,
                        fontSize: 14.sp)),
                TextSpan(
                    text: '张',
                    style: TextStyles.display10.copyWith(
                        fontWeight: AppFontWeight.semiBold(),
                        color: Colours.colorFFF280)),
                TextSpan(
                    text: "个人赛事券（免费解锁个人比赛报告）", style: TextStyles.display10),
              ],
            )),
          ],
        ),
      ),
    );
  }

  Widget _selPlace(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 24.w),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                '选择球馆',
                style: TextStyles.semiBold,
              ),
              SizedBox(
                width: 8.w,
              ),
              Text(
                'VIP仅可在您选择的场馆使用',
                style: TextStyles.regular
                    .copyWith(color: Colours.color5C5C6E, fontSize: 12.sp),
              ),
            ],
          ),
          SizedBox(
            height: 20.w,
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => logic.showPlaceSheet(context),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 15.w),
              height: 54.w,
              decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.circular(16.w),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Obx(
                      () => Text(
                        state.place.value == null
                            ? '请点此选择球馆'
                            : state.place.value!.arenaName,
                        style: TextStyles.regular,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  WxAssets.images.icVipArrow
                      .image(width: 14.w, height: 14.w, fit: BoxFit.fill),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
