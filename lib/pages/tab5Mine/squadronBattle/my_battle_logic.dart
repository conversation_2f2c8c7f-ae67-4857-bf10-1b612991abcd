import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_bus.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/my_matches_model.dart';
import 'package:shoot_z/utils/event_bus.dart';

class MyBattleLogic extends GetxController {
  var myBattleModelList = <MyBattleModel>[].obs;
  RefreshController refreshController =
      RefreshController(initialRefresh: false);

  /// 是否正在加载数据
  bool _isLoading = false;
  var init = false.obs;
  var page = 1;
  var pageSize = 10;
  var totalRows = 0;
  @override
  void onInit() {
    super.onInit();
    onRefresh();
  }

  @override
  void onReady() {
    super.onReady();
  }

  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }
    if (!hasMore()) {
      return;
    }
    await myMatches(false);
  }

  bool hasMore() {
    return myBattleModelList.length < totalRows;
  }

  Future<void> onRefresh() async {
    if (_isLoading) {
      return;
    }
    page = 1;
    await myMatches(true);
    // init.value = true;
  }

  //我的约战
  Future<void> myMatches(bool isRefresh) async {
    _isLoading = true;
    WxLoading.show();
    Map<String, dynamic> request = {'limit': pageSize, 'page': page};
    var res = await Api().get(ApiUrl.myMatchesList, queryParameters: request);
    _isLoading = false;
    init.value = true;
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      cc.log("result${res.data}");
      List dataList = res.data["list"];
      myBattleModelList.value =
          dataList.map((e) => MyBattleModel.fromJson(e)).toList();
      totalRows = res.data["total"];
    } else {
      if (isRefresh) {
        myBattleModelList.value = [];
        totalRows = 0;
      }
      WxLoading.showToast(res.message);
    }
  }

  //删除约战
  Future<void> deleteMatches(String challengeId) async {
    WxLoading.show();
    Map<String, dynamic> request = {
      'challengeId': challengeId,
    };
    var res = await Api().post(ApiUrl.deleteChallenge, data: request);
    WxLoading.dismiss();
    if (res.isSuccessful()) {
      WxLoading.showToast('已删除');
      //通知首页刷新数据
      BusUtils.instance
          .fire(EventAction(key: EventBusKey.createSquadronBattle));
      onRefresh();
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
