import 'package:json_annotation/json_annotation.dart';

import '../../../../generated/l10n.dart';

part 'place_model.g.dart';

@JsonSerializable()
class PlaceModel extends Object {
  @Json<PERSON>ey(name: 'address')
  String address;

  @J<PERSON><PERSON><PERSON>(name: 'arenaID')
  int arenaID;

  @Json<PERSON><PERSON>(name: 'arenaName')
  String arenaName;

  @Json<PERSON>ey(name: 'beginTime')
  String beginTime;

  @JsonKey(name: 'cityID')
  int cityID;

  @Json<PERSON>ey(name: 'createBy')
  int createBy;

  @Json<PERSON>ey(name: 'createdTime')
  String createdTime;

  @Json<PERSON><PERSON>(name: 'deleted')
  int deleted;

  @Json<PERSON>ey(name: 'description')
  String description;

  @Json<PERSON><PERSON>(name: 'distance')
  double distance;

  @Json<PERSON>ey(name: 'enabled')
  int enabled;

  @Json<PERSON>ey(name: 'endTime')
  String endTime;

  @JsonKey(name: 'floorCondition')
  String floorCondition;

  @<PERSON>son<PERSON>ey(name: 'latitude')
  double latitude;

  @<PERSON>son<PERSON><PERSON>(name: 'logo')
  String logo;

  @Json<PERSON>ey(name: 'longitude')
  double longitude;

  @Json<PERSON><PERSON>(name: 'provinceID')
  String provinceID;

  @Json<PERSON>ey(name: 'stage')
  int stage;

  @JsonKey(name: 'tel')
  String tel;

  @JsonKey(name: 'type')
  int type;

  @JsonKey(name: 'updateBy')
  int updateBy;

  @JsonKey(name: 'updatedTime')
  String updatedTime;

  @JsonKey(name: 'isBuy')
  bool isBuy;

  @JsonKey(name: 'isReport')
  bool isReport;

  String get label {
    var label = '';
    switch (type) {
      case 1:
        label = S.current.often_go;
        break;
      case 2:
        label = S.current.last_time_i_went;
        break;
      case 3:
        label = S.current.closest_to_me;
        break;
      case 4:
        label = S.current.been;
        break;
    }
    return label;
  }

  PlaceModel(
    this.address,
    this.arenaID,
    this.arenaName,
    this.beginTime,
    this.cityID,
    this.createBy,
    this.createdTime,
    this.deleted,
    this.description,
    this.distance,
    this.enabled,
    this.endTime,
    this.floorCondition,
    this.latitude,
    this.logo,
    this.longitude,
    this.provinceID,
    this.stage,
    this.tel,
    this.type,
    this.updateBy,
    this.updatedTime,
    this.isBuy,
    this.isReport,
  );

  factory PlaceModel.fromJson(Map<String, dynamic> srcJson) =>
      _$PlaceModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$PlaceModelToJson(this);
}
