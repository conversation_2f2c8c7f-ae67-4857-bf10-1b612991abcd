// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'place_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlaceModel _$PlaceModelFromJson(Map<String, dynamic> json) => PlaceModel(
      json['address'] as String,
      json['arenaID'] as int,
      json['arenaName'] as String,
      json['beginTime'] as String,
      json['cityID'] as int,
      json['createBy'] as int,
      json['createdTime'] as String,
      json['deleted'] as int,
      json['description'] as String,
      (json['distance'] as num).toDouble(),
      json['enabled'] as int,
      json['endTime'] as String,
      json['floorCondition'] as String,
      (json['latitude'] as num).toDouble(),
      json['logo'] as String,
      (json['longitude'] as num).toDouble(),
      json['provinceID'] as String,
      json['stage'] as int,
      json['tel'] as String,
      json['type'] as int,
      json['updateBy'] as int,
      json['updatedTime'] as String,
      json['isBuy'] as bool,
      json['isReport'] as bool,
    );

Map<String, dynamic> _$PlaceModelToJson(PlaceModel instance) =>
    <String, dynamic>{
      'address': instance.address,
      'arenaID': instance.arenaID,
      'arenaName': instance.arenaName,
      'beginTime': instance.beginTime,
      'cityID': instance.cityID,
      'createBy': instance.createBy,
      'createdTime': instance.createdTime,
      'deleted': instance.deleted,
      'description': instance.description,
      'distance': instance.distance,
      'enabled': instance.enabled,
      'endTime': instance.endTime,
      'floorCondition': instance.floorCondition,
      'latitude': instance.latitude,
      'logo': instance.logo,
      'longitude': instance.longitude,
      'provinceID': instance.provinceID,
      'stage': instance.stage,
      'tel': instance.tel,
      'type': instance.type,
      'updateBy': instance.updateBy,
      'updatedTime': instance.updatedTime,
      'isBuy': instance.isBuy,
      'isReport': instance.isReport,
    };
