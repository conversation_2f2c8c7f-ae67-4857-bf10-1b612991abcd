import 'package:json_annotation/json_annotation.dart';

part 'user_info_model.g.dart';

@JsonSerializable()
class UserInfoModel extends Object {
  bool get isVip => vipLevel != 0;
  bool get isSVip => vipLevel == 2;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'age')
  int age;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'avatar')
  String avatar;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'birthday')
  String birthday;

  @<PERSON>son<PERSON><PERSON>(name: 'gender')
  int gender;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'isSignToday')
  bool isSignToday;

  @<PERSON>son<PERSON>ey(name: 'point')
  int point;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'registered')
  bool registered;

  @<PERSON>son<PERSON>ey(name: 'betaUser')
  bool betaUser;

  @<PERSON>son<PERSON><PERSON>(name: 'signInPoints')
  int signInPoints;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'userId')
  String userId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'userName')
  String userName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'vipLevel')
  int vipLevel;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'nextDayPoints')
  int nextDayPoints;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'vipExpiredFlag')
  int? vipExpiredFlag;

  @<PERSON>son<PERSON><PERSON>(name: 'vipExpiredDays')
  int? vipExpiredDays;

  @JsonKey(name: 'vipExpireDate')
  String? vipExpireDate;
  @JsonKey(name: 'inviteCode')
  String? inviteCode;

  @JsonKey(name: 'vipArenaId')
  int? vipArenaId;

  @JsonKey(name: 'vipArenaName')
  String? vipArenaName;

  UserInfoModel(
      this.age,
      this.avatar,
      this.birthday,
      this.gender,
      this.isSignToday,
      this.point,
      this.registered,
      this.betaUser,
      this.signInPoints,
      this.userId,
      this.userName,
      this.vipLevel,
      this.vipArenaId,
      this.nextDayPoints,
      this.vipArenaName,
      {this.vipExpiredFlag,
      this.vipExpiredDays,
      this.vipExpireDate,
      this.inviteCode});

  factory UserInfoModel.fromJson(Map<String, dynamic> srcJson) =>
      _$UserInfoModelFromJson(srcJson);

  Map<String, dynamic> toJson() => _$UserInfoModelToJson(this);
}
