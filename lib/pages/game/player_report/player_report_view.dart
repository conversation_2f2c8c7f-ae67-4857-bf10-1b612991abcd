import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/game/player_report/player_report_logic.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/mytext.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///比赛  球员报告
class PlayerReportPage extends StatelessWidget {
  PlayerReportPage({super.key});
  final logic = Get.put(PlayerReportLogic());
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Scaffold(
        appBar: MyAppBar(
          title: Text(S.current.player_report),
          actions: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () => MyShareH5.getShareH5(SharePlayerReport(
                  matchId: logic.matchId.value,
                  teamId: logic.teamId.value,
                  playerId: logic.playerId.value)),
              child: Container(
                width: 40.w,
                height: 40.w,
                alignment: Alignment.centerRight,
                child: WxAssets.images.share3
                    .image(color: Colors.white, width: 22.w, height: 22.w),
              ),
            ),
            SizedBox(
              width: 18.w,
            ),
          ],
        ),
        body: (logic.isFrist.value)
            ? buildLoad()
            : (logic.playerReportModel.value.playerId == null ||
                    logic.playerReportModel.value.playerId == "")
                ? SizedBox(
                    height: 300.w,
                    child: myNoDataView(
                      context,
                      msg: S.current.no_matches_yet,
                      imagewidget: WxAssets.images.icGameNo
                          .image(width: 150.w, height: 150.w),
                    ))
                : _createTeamWidget(context),
        bottomNavigationBar: (logic.isFrist.value) ||
                (logic.playerReportModel.value.playerId == null ||
                    logic.playerReportModel.value.playerId == "")
            ? const SizedBox()
            : Container(
                width: double.infinity,
                padding: EdgeInsets.only(bottom: 25.w, top: 10.w),
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () => MyShareH5.getShareH5(SharePlayerReport(
                      matchId: logic.matchId.value,
                      teamId: logic.teamId.value,
                      playerId: logic.playerId.value)),
                  child: Container(
                    height: 46.w,
                    width: double.infinity,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(left: 20.w, right: 20.w),
                    decoration: BoxDecoration(
                      color: Colours.color282735,
                      borderRadius: BorderRadius.all(Radius.circular(28.r)),
                      gradient: const LinearGradient(
                        colors: [Colours.color7732ED, Colours.colorA555EF],
                        begin: Alignment.bottomLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: Text(
                      S.current.player_report_tips16,
                      style: TextStyles.display16.copyWith(fontSize: 16.sp),
                    ),
                  ),
                ),
              ),
      );
    });
  }

  /// 列表数据
  _createTeamWidget(BuildContext context) {
    return Obx(() {
      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              height: 15.w,
            ),
            Container(
              width: double.infinity,
              height: 151.w,
              margin: EdgeInsets.symmetric(horizontal: 15.w),
              child: Stack(
                children: [
                  WxAssets.images.imgPlayer2
                      .image(width: 375.w, height: 151.w, fit: BoxFit.fitWidth),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            margin: EdgeInsets.only(left: 15.w),
                            decoration: BoxDecoration(
                                border: Border.all(
                                    width: 1.w, color: Colours.white),
                                borderRadius: BorderRadius.circular(8.r)),
                            child: MyImage(
                              (logic.playerReportModel.value.boundUserPhoto ??
                                          "") !=
                                      ""
                                  ? logic.playerReportModel.value
                                          .boundUserPhoto ??
                                      ""
                                  : logic.playerReportModel.value.photo ?? "",
                              width: 74.w,
                              height: 99.w,
                              radius: 8.r,
                            ),
                          ),
                          SizedBox(
                            width: 15.w,
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: 35.w,
                                ),
                                Row(
                                  children: [
                                    Container(
                                      width: 17.w,
                                      height: 20.w,
                                      child: Stack(
                                        alignment: Alignment.center,
                                        children: [
                                          MyImage(
                                            "img_player1.png",
                                            width: 17.w,
                                            height: 20.w,
                                            isAssetImage: true,
                                            imageColor: (logic.playerReportModel
                                                            .value.number ??
                                                        "") !=
                                                    ""
                                                ? Colours.white
                                                : null,
                                          ),
                                          Text(
                                              logic.playerReportModel.value
                                                      .number ??
                                                  "",
                                              textAlign: TextAlign.right,
                                              style: GoogleFonts.oswald(
                                                  fontWeight:
                                                      AppFontWeight.medium(),
                                                  fontSize: 12.sp,
                                                  color: Colours.colorA44EFF)),
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      width: 9.w,
                                    ),
                                    Text(
                                      !(logic.playerReportModel.value
                                                  .bindable ??
                                              false)
                                          ? logic.playerReportModel.value
                                                  .boundUserName ??
                                              ""
                                          : S.current.player_report_claimed,
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 18.sp,
                                          fontWeight: FontWeight.w600),
                                    )
                                  ],
                                ),
                                SizedBox(
                                  height: 20.w,
                                ),
                                Row(
                                  children: [
                                    MyImage(
                                      logic.playerReportModel.value.teamLogo ??
                                          "",
                                      width: 18.w,
                                      height: 18.w,
                                      radius: 10.r,
                                      isAssetImage: false,
                                      errorImage: "my_team_head4.png",
                                    ),
                                    SizedBox(
                                      width: 5.w,
                                    ),
                                    Text(
                                      logic.playerReportModel.value.teamName ??
                                          "",
                                      style: TextStyles.regular.copyWith(
                                        fontSize: 12.sp,
                                        color: Colours.colorD5B6F8,
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              SizedBox(
                                height: 40.w,
                                child: logic.playerReportModel.value.mvp == true
                                    ? Transform.translate(
                                        offset: const Offset(-8, -10),
                                        child: WxAssets.images.imgPlayer5
                                            .image(width: 110.w, height: 43.w),
                                      )
                                    : const SizedBox(),
                              ),
                              if (logic.playerReportModel.value.bindable ==
                                  true)
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    //认领报告
                                    if (UserManager.instance.isLogin) {
                                      logic.bindReport();
                                    } else {
                                      AppPage.to(Routes.login).then((value) {
                                        if (value) {
                                          logic.bindReport();
                                        }
                                      });
                                    }
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(right: 15.w),
                                    padding: EdgeInsets.only(
                                        left: 22.w,
                                        right: 22.w,
                                        bottom: 9.w,
                                        top: 9.w),
                                    decoration: BoxDecoration(
                                        gradient: const LinearGradient(colors: [
                                          Color(0xFFFFF9DC),
                                          Color(0xFFE4C8FF),
                                          Color(0xFFE5F3FF)
                                        ]),
                                        borderRadius:
                                            BorderRadius.circular(20.r)),
                                    child: Text(
                                      S.current.player_report_report,
                                      style: TextStyle(
                                          color: Colours.color0F0F16,
                                          fontSize: 14.sp,
                                          fontWeight: FontWeight.w600),
                                    ),
                                  ),
                                ),
                              if (logic.playerReportModel.value.bindable !=
                                      true &&
                                  UserManager.instance.user?.userId ==
                                      logic.playerReportModel.value
                                          .boundUserId &&
                                  UserManager.instance.user?.userId != "")
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    //解绑
                                    logic.unBindReport();
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(right: 15.w),
                                    padding: EdgeInsets.only(
                                        left: 22.w, bottom: 9.w, top: 0.w),
                                    decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(20.r)),
                                    child: Row(
                                      children: [
                                        WxAssets.images.imgUnbind
                                            .image(width: 12.w, height: 12.w),
                                        SizedBox(
                                          width: 5.w,
                                        ),
                                        Text(
                                          S.current.unbind,
                                          style: TextStyle(
                                              color: Colours.colorD5B6F8,
                                              fontSize: 12.sp,
                                              fontWeight: FontWeight.w600),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                            ],
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 14.w,
                      ),
                      Wrap(
                        spacing: 8.w,
                        children:
                            List.generate(logic.playerTaglist.length, (index) {
                          return Container(
                            // width: 50.w,
                            padding: EdgeInsets.all(4.w),
                            margin:
                                EdgeInsets.only(left: index == 0 ? 15.w : 0),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(25.r),
                                gradient: const LinearGradient(colors: [
                                  Color(0x20AEFFAD),
                                  Color(0x30FFF870)
                                ])),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                MyImage(
                                  logic.playerTaglist[index]["img"] ?? "",
                                  width: 9.w,
                                  height: 9.w,
                                  isAssetImage: true,
                                  imageColor: logic
                                              .playerReportModel.value.title
                                              ?.contains(
                                                  logic.playerTaglist[index]
                                                          ["name"] ??
                                                      "") ??
                                          false
                                      ? null
                                      : Colours.colorD5B6F8,
                                ),
                                SizedBox(
                                  width: 2.w,
                                ),
                                Text(
                                  logic.playerTaglist[index]["name"] ?? "",
                                  style: TextStyles.regular.copyWith(
                                      fontSize: 10.sp,
                                      color: logic.playerReportModel.value.title
                                                  ?.contains(
                                                      logic.playerTaglist[index]
                                                              ["name"] ??
                                                          "") ??
                                              false
                                          ? Colours.white
                                          : Colours.colorD5B6F8),
                                )
                              ],
                            ),
                          );
                        }),
                      )
                    ],
                  ),
                  Positioned(
                      bottom: 0.w,
                      right: 0.w,
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          AppPage.to(Routes.careerHighlightsHomePage,
                              arguments: {
                                'userId': logic.playerReportModel.value.playerId
                              },
                              needLogin: true);
                        },
                        child: Container(
                          width: 45.w,
                          height: 50.w,
                          color: Colors.transparent,
                        ),
                      ))
                ],
              ),
            ),
            //球员数据
            Container(
              height: 55.w,
              margin: EdgeInsets.only(top: 10.w, left: 15.w, right: 15.w),
              child: Row(
                children: [
                  Text(
                    S.current.player_report_member_data,
                    style: TextStyles.regular.copyWith(fontSize: 16.sp),
                  ),
                  const Spacer(),
                  // WxAssets.images.imgPlayerData
                  //     .image(width: 16.w, height: 16.w),
                  // SizedBox(
                  //   width: 5.w,
                  // ),
                  // Text(
                  //   S.current.player_report_placard,
                  //   style: TextStyles.regular
                  //       .copyWith(fontSize: 12.sp, color: Colours.color9393A5),
                  // ),
                ],
              ),
            ),
            Container(
              // height: 55.w,
              margin: EdgeInsets.only(left: 15.w, right: 15.w),
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(12.r)),
              child: GridView.builder(
                  scrollDirection: Axis.vertical,
                  // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                  shrinkWrap: true,
                  physics:
                      const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: 15,
                    mainAxisSpacing: 20,
                    childAspectRatio: 101 / 52,
                  ),
                  padding: EdgeInsets.only(bottom: 0.w),
                  itemCount: logic.playerDatalist.length,
                  itemBuilder: (context, index) {
                    return Obx(() {
                      return GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {},
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(logic.playerDatalist[index]["data"] ?? "0",
                                textAlign: TextAlign.right,
                                style: GoogleFonts.oswald(
                                    fontWeight: AppFontWeight.medium(),
                                    fontSize: 18.sp,
                                    color: Colours.white)),
                            const Spacer(),
                            Text(
                              logic.playerDatalist[index]["name"] ?? "",
                              textAlign: TextAlign.right,
                              style: TextStyles.medium.copyWith(
                                  fontSize: 14.sp, color: Colours.color5C5C6E),
                            ),
                          ],
                        ),
                      );
                    });
                  }),
            ),
            if (!(logic.playerReportModel.value.sectionsScore?.isEmpty ?? true))
              //小节比分
              Container(
                height: 55.w,
                margin: EdgeInsets.only(top: 10.w, left: 15.w, right: 15.w),
                child: Row(
                  children: [
                    Text(
                      S.current.player_report_tips12,
                      style: TextStyles.regular.copyWith(fontSize: 16.sp),
                    ),
                  ],
                ),
              ),
            if (!(logic.playerReportModel.value.sectionsScore?.isEmpty ?? true))
              Container(
                // height: 55.w,
                margin: EdgeInsets.only(left: 15.w, right: 15.w),
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.circular(12.r)),
                child: GridView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    physics:
                        const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 5,
                      crossAxisSpacing: 15,
                      mainAxisSpacing: 20,
                      childAspectRatio: 1,
                    ),
                    padding: EdgeInsets.only(bottom: 0.w),
                    itemCount: logic.playerScorelist.length,
                    itemBuilder: (context, index) {
                      return Obx(() {
                        return GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {},
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                logic.playerScorelist[index]["name"] ?? "",
                                textAlign: TextAlign.right,
                                style: TextStyles.medium.copyWith(
                                    fontSize: 14.sp,
                                    color: Colours.color5C5C6E),
                              ),
                              const Spacer(),
                              Text(
                                logic.playerScorelist[index]["data"] ?? "0",
                                textAlign: TextAlign.right,
                                style: TextStyles.regular
                                    .copyWith(fontSize: 14.sp),
                              ),
                            ],
                          ),
                        );
                      });
                    }),
              ),
            //高光集锦
            Container(
              height: 55.w,
              margin: EdgeInsets.only(top: 10.w, left: 15.w, right: 15.w),
              alignment: Alignment.centerLeft,
              child: Text(
                S.current.player_report_tips14,
                style: TextStyles.regular.copyWith(fontSize: 16.sp),
              ),
            ),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                AppPage.to(Routes.videos, arguments: {
                  "videoId": logic.playerReportModel.value.videoMergeId,
                  "matchId": logic.matchId.value,
                  "type": "0", //0合并id  1片段id
                });
              },
              child: Container(
                margin: EdgeInsets.only(left: 15.w, right: 15.w),
                width: double.infinity,
                height: 194.w,
                decoration:
                    BoxDecoration(borderRadius: BorderRadius.circular(12.r)),
                //videoStatus	integer 视频状态 0，1 合成中，2 合成完成，3 合成失败
                child: logic.playerReportModel.value.videoStatus != 2
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          WxAssets.images.icVideoScz.image(
                              width: 80.w, height: 76.w, fit: BoxFit.fill),
                          const SizedBox(
                            height: 21,
                          ),
                          MyText(
                            S.current.highlights_generate,
                            size: 14,
                            color: Colours.color5C5C6E,
                          ),
                        ],
                      )
                    : MyImage(
                        logic.playerReportModel.value.videoCover ?? "",
                        width: double.infinity,
                        height: 194.w,
                        radius: 12.r,
                        errorImage: "error_image_width.png",
                        placeholderImage: "error_image_width.png",
                      ),
              ),
            ),

            Container(
              height: 55.w,
              margin: EdgeInsets.only(top: 10.w, left: 15.w, right: 15.w),
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  Text(
                    S.current.player_report_tips15,
                    style: TextStyles.regular.copyWith(fontSize: 16.sp),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: () {
                      //去剪辑
                      AppPage.to(Routes.optionPlayerGoalPage, arguments: {
                        "matchId": logic.matchId.value,
                        "playerId": logic.playerId.value,
                        "teamId": logic.teamId.value,
                        "type": "0", //0合并id  1片段id
                      });
                      log("getTeamPlayerReport2=${{
                        "matchId": logic.matchId.value,
                        "playerId": logic.playerId.value,
                        "teamId": logic.teamId.value,
                        "type": "0", //0合并id  1片段id
                      }}");
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          S.current.player_report_tips18,
                          style: TextStyles.display12
                              .copyWith(color: Colours.color9393A5),
                        ),
                        const SizedBox(
                          width: 2,
                        ),
                        WxAssets.images.icArrowRight
                            .image(width: 14, height: 14)
                      ],
                    ),
                  )
                ],
              ),
            ),
            (logic.isFrist.value)
                ? buildLoad()
                : (logic.playerReportModel.value.videos?.isEmpty ?? true)
                    ? SizedBox(
                        height: 300.w,
                        child: myNoDataView(
                          context,
                          msg: S.current.No_data_available,
                          imagewidget: WxAssets.images.icGameNo
                              .image(width: 150.w, height: 150.w),
                        ))
                    : Container(
                        // height: 55.w,
                        margin: EdgeInsets.only(left: 15.w, right: 15.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12.r)),
                        child: GridView.builder(
                            scrollDirection: Axis.vertical,
                            // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                            shrinkWrap: true,
                            physics:
                                const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2,
                              crossAxisSpacing: 15,
                              mainAxisSpacing: 15,
                              childAspectRatio: 166 / 95,
                            ),
                            padding: EdgeInsets.only(bottom: 40.w),
                            itemCount:
                                logic.playerReportModel.value.videos?.length,
                            itemBuilder: (context, index) {
                              return Obx(() {
                                return GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () {
                                    AppPage.to(Routes.videos, arguments: {
                                      "videoId": logic.playerReportModel.value
                                          .videos?[index]?.videoId,
                                      "matchId": logic.matchId.value,
                                      "type": "1", //0合并id  1片段id
                                    });
                                  },
                                  child: Stack(
                                    children: [
                                      MyImage(
                                        logic.playerReportModel.value
                                                .videos?[index]?.videoCover ??
                                            "",
                                        width: double.infinity,
                                        height: 194.w,
                                        radius: 12.r,
                                        errorImage: "error_image.png",
                                        placeholderImage: "error_image.png",
                                      ),
                                      Positioned(
                                        left: 5.w,
                                        top: 5.w,
                                        child: Container(
                                          padding: EdgeInsets.only(
                                              left: 7.w,
                                              right: 7.w,
                                              top: 5.w,
                                              bottom: 5.w),
                                          decoration: BoxDecoration(
                                              color: Colours.color50000000,
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(4.r))),
                                          child: Text(
                                            logic
                                                    .playerReportModel
                                                    .value
                                                    .videos?[index]
                                                    ?.markedText ??
                                                "",
                                            textAlign: TextAlign.right,
                                            style: TextStyles.medium.copyWith(
                                                fontSize: 10.sp,
                                                color: Colours.white),
                                          ),
                                        ),
                                      ),
                                      Positioned(
                                        right: 5.w,
                                        bottom: 5.w,
                                        child: Container(
                                          padding: EdgeInsets.only(
                                              left: 7.w,
                                              right: 7.w,
                                              top: 5.w,
                                              bottom: 5.w),
                                          decoration: BoxDecoration(
                                              color: Colours.color50000000,
                                              borderRadius: BorderRadius.only(
                                                  topLeft: Radius.circular(4.r),
                                                  topRight:
                                                      Radius.circular(4.r),
                                                  bottomLeft:
                                                      Radius.circular(4.r),
                                                  bottomRight:
                                                      Radius.circular(12.r))),
                                          child: Text(
                                            "${(((logic.playerReportModel.value.videos?[index]?.duration ?? 0) > 0)) ? logic.formatDuration(logic.playerReportModel.value.videos?[index]?.duration ?? 0) : logic.playerReportModel.value.videos?[index]?.duration ?? ""}",
                                            textAlign: TextAlign.right,
                                            style: TextStyles.medium.copyWith(
                                                fontSize: 10.sp,
                                                color: Colours.white),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              });
                            }),
                      ),
          ],
        ),
      );
    });
  }
}
