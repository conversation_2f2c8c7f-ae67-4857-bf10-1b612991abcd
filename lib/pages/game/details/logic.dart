import 'dart:math' as math2;
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/api_url.dart';
import 'package:shoot_z/network/model/mactches_info_model.dart';
import 'package:shoot_z/pages/game/details/models/match_pay_info_model.dart';
import 'package:shoot_z/pages/game/details/models/section_score_model.dart';
import 'package:shoot_z/pages/game/details/state.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/myShareH5.dart';
import 'package:shoot_z/widgets/video/video_controller.dart';
import 'package:ui_packages/ui_packages.dart';
import '../../../main.dart';
import 'dart:developer';

class GameDetailsLogic extends GetxController
    with WidgetsBindingObserver, RouteAware, GetSingleTickerProviderStateMixin {
  final state = GameDetailsState();
  final TextEditingController textController = TextEditingController();
  FixedExtentScrollController teamController = FixedExtentScrollController();
  var teams = <String>[].obs;
  final currentLength = 0.obs;
  var rate = 0.0.obs;
  var feedbackType = 1.obs;
  var suggestionType = 0.obs;
  var selectedTeamIndex = 0.obs;
  var selectedTeamName = ''.obs;
  var selectedTeamId = '';
  TabController? tabController;
  var tabbarIndex = 0.obs;
  //球员列表数据
  var dataPlayerList1 = <MatchPayInfoModelLockedPlayers>[].obs;
  var dataPlayerList2 = <MatchPayInfoModelLockedPlayers>[].obs;
  var selectedPlayer = MatchPayInfoModelLockedPlayers().obs; //选择的解锁的球员
  var selectedPlayerId = "".obs;
  void disposeVideo() {
    state.videoController.dispose();
  }

  void switchTab(index) {
    //  tabbarIndex.value = index;
    tabController?.index = index;
  }

  @override
  void didPopNext() {
    if (!state.videoController.fromFullScreen) {
      getData(true);
    }
    super.didPopNext();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState s) {
    super.didChangeAppLifecycleState(s);
    if (s == AppLifecycleState.resumed && state.init.value) {
      getData(true);
    }
  }

  var isSubscribe = false;
  void subscribe(BuildContext context) {
    if (isSubscribe) return;
    final ModalRoute? route = ModalRoute.of(context);
    if (route is PageRoute) {
      routeObserver.subscribe(this, route);
      isSubscribe = true;
    }
  }

  late String matchId;
  @override
  void onInit() {
    matchId = Get.arguments;
    WidgetsBinding.instance.addObserver(this);
    getData(false);
    state.scrollController.addListener(_scrollListener);
    state.videoController =
        VideoController(videoPath: state.matchVideoPath.value);
    getPlayerInfo();
    super.onInit();
    tabController = TabController(length: 2, vsync: this);
    tabController?.addListener(
      () {
        tabbarIndex.value = tabController?.index ?? 0;
      },
    );
  }

  void getData(bool refresh) async {
    final results = await getMatches();
    if (!refresh) {
      if (results) {
        state.init.value = true;
      } else {
        AppPage.back();
      }
    }
  }

  void scoreSubmit() {
    if (rate.value == 0) {
      WxLoading.showToast('请选择评分');
      return;
    }
    if (selectedTeamId == '') {
      WxLoading.showToast('请选择球队');
      return;
    }
    if (selectedPlayer.value.playerId == null) {
      WxLoading.showToast('请选择球员');
      return;
    }
  }
  // Future<bool> getPayInfo(bool refresh) async {
  //   final res = await Api().get(ApiUrl.getMatchIdPayInfo(matchId));
  //   if(res.isSuccessful()) {
  //     if(!refresh) {
  //       state.payInfo = MatchPayInfoModel.fromJson(res.data).obs;
  //     }
  //     else {
  //       state.payInfo.value = MatchPayInfoModel.fromJson(res.data);
  //       state.payInfo.refresh();
  //     }
  //     return true;
  //   }
  //   return false;
  // }

  Future<bool> getMatches() async {
    final res = await Api().get(ApiUrl.getMatches(matchId));
    if (res.isSuccessful()) {
      state.model.value = MatchesInfoModel.fromJson(res.data);
      state.allLocked.value = state.model.value.locked == 0;
      state.leftTeam.value = state.model.value.teams?.first!.teamBest ??
          MatchesInfoModelTeamsTeamBest();
      state.rightTeam.value = state.model.value.teams?.last?.teamBest ??
          MatchesInfoModelTeamsTeamBest();
      state.leftTeam.refresh();
      state.rightTeam.refresh();
      final sectionRes = await Api().get(ApiUrl.getSectionScore(matchId));
      if (sectionRes.isSuccessful()) {
        state.sectionScoreList = (sectionRes.data as List)
            .map((e) => SectionScoreModel.fromJson(e))
            .toList();
      }
      if (state.matchVideoPath.value != state.model.value.matchVideoPath) {
        state.matchVideoPath.value = state.model.value.matchVideoPath ?? "";
        state.videoController.setData(videoPath: state.matchVideoPath.value);
      }
      return true;
    }
    return false;
  }

  void unlockPlayer(String teamId, String playerId, bool locking) {
    if (locking) {
      // UnlockPayUtils.pay(matchId, teamId: teamId, playerId: playerId);
      AppPage.to(Routes.unlockDataPage,
          arguments: {
            "type": 2,
            "matchId": matchId,
            "teamId": teamId,
            "playerId": playerId,
          },
          needLogin: true);
    } else {
      AppPage.to(Routes.playerReportPage, arguments: {
        "teamId": teamId,
        "playerId": playerId,
        "matchId": matchId
      });
    }
  }

  getPlayerInfo() async {
    if (!UserManager.instance.isLogin) return true;
    final res = await Api().get(ApiUrl.getMatchIdPayInfo(matchId));
    if (res.isSuccessful()) {
      log("!!!!!!!!!!${res.data}");
      var matchPayInfoModel = MatchPayInfoModel.fromJson(res.data);
      // var teamsId1 = matchPayInfoModel.teams?.first?.teamId;
      dataPlayerList1.value = (matchPayInfoModel.teams?.first?.players ?? [])
          .where((player) => player != null)
          .cast<MatchPayInfoModelLockedPlayers>()
          .toList();
      dataPlayerList2.value = (matchPayInfoModel.teams?.last?.players ?? [])
          .where((player) => player != null)
          .cast<MatchPayInfoModelLockedPlayers>()
          .toList();
    }
  }

  void unlockAll() {
    if (state.allLocked.value) {
      share();
      return;
    } else {
      //解锁报告 type 0整场解锁  1单队解锁 2个人解锁
      AppPage.to(Routes.unlockDataPage,
          arguments: {"type": 0, "matchId": matchId}, needLogin: true); //0

      //   .then((onValue) {
      // if (onValue == "1") {
      //   getData(true);
      // }
      //}
    }
    // UnlockPayUtils.pay(matchId);
  }

  void share() {
    MyShareH5.getShareH5(ShareGameDetails(matchId: matchId));
  }

  @override
  void onClose() {
    state.scrollController.removeListener(_scrollListener);
    state.scrollController.dispose();
    disposeVideo();
    WidgetsBinding.instance.removeObserver(this);
    routeObserver.unsubscribe(this);
    super.onClose();
  }

  void _scrollListener() {
    // 获取当前滚动的位置
    double offset = state.scrollController.offset;
    if (offset >= state.offset && !state.showTop.value) {
      state.showTop.value = true;
    } else if (offset < state.offset && state.showTop.value) {
      state.showTop.value = false;
    }
  }

  LineChartData get lineChartData => LineChartData(
      lineTouchData: lineTouchData, //线触摸数据
      gridData: gridData,
      titlesData: titlesData, //四周坐标抽数据
      borderData: borderData, //坐标轴线条
      lineBarsData: lineBarsData, //线条数据
      minX: 0,
      maxX: (state.model.value.teams?.first?.scoreTrend?.length ?? 0) - 1,
      maxY: math2.max(
        (state.model.value.teams?.first?.scoreTrend?.last ?? 0).toDouble(),
        (state.model.value.teams?.last?.scoreTrend?.last ?? 0).toDouble(),
      ),
      minY: math2.min(
        (state.model.value.teams?.first?.scoreTrend?.first ?? 0).toDouble(),
        (state.model.value.teams?.last?.scoreTrend?.first ?? 0).toDouble(),
      ));

  FlBorderData get borderData => FlBorderData(
        show: false,
      );

  LineTouchData get lineTouchData => LineTouchData(
        handleBuiltInTouches: true,
        getTouchedSpotIndicator: defaultTouchedIndicators,
        touchTooltipData: LineTouchTooltipData(
            getTooltipColor: (touchedSpot) => Colours.color15151D,
            tooltipBorder:
                const BorderSide(color: Colours.color2F2F3B, width: 1),
            tooltipMargin: 16,
            tooltipPadding:
                const EdgeInsets.symmetric(horizontal: 10, vertical: 7),
            getTooltipItems: defaultLineTooltipItem),
      );

  List<LineTooltipItem> defaultLineTooltipItem(
          List<LineBarSpot> touchedSpots) =>
      touchedSpots.map((LineBarSpot touchedSpot) {
        final textStyle = TextStyle(
          color: touchedSpot.bar.gradient?.colors.first ??
              touchedSpot.bar.color ??
              Colors.blueGrey,
          fontWeight: AppFontWeight.semiBold(),
          fontSize: 12.sp,
        );
        return LineTooltipItem(touchedSpot.y.toString(), textStyle);
      }).toList();

  List<TouchedSpotIndicatorData> defaultTouchedIndicators(
    LineChartBarData barData,
    List<int> indicators,
  ) =>
      indicators.map((int index) {
        /// Indicator Line
        const flLine = FlLine(color: Colors.transparent, strokeWidth: 0);
        var dotSize = 6.0;
        final dotData = FlDotData(
          getDotPainter: (spot, percent, bar, index) =>
              _defaultGetDotPainter(spot, percent, bar, index, size: dotSize),
        );

        return TouchedSpotIndicatorData(flLine, dotData);
      }).toList();

  FlDotPainter _defaultGetDotPainter(
    FlSpot spot,
    double xPercentage,
    LineChartBarData bar,
    int index, {
    double? size,
  }) =>
      FlDotCirclePainter(
          radius: size,
          color: _defaultGetDotColor(spot, xPercentage, bar),
          strokeColor: Colors.white,
          strokeWidth: 2);

  Color _defaultGetDotColor(
      FlSpot _, double xPercentage, LineChartBarData bar) {
    return bar.gradient?.colors.first ?? bar.color ?? Colors.blueGrey;
  }

  FlGridData get gridData => FlGridData(
        show: false,
        drawHorizontalLine: false,
        verticalInterval: 1,
        getDrawingVerticalLine: (value) {
          return const FlLine(
            color: Colours.color282835,
            strokeWidth: 0.5,
            dashArray: [5, 5],
          );
        },
        checkToShowVerticalLine: (value) {
          // if(value != 0 || value != 4) {
          return true;
          // }
          // return false;
        },
      );

  FlTitlesData get titlesData => const FlTitlesData(
        show: false,
      );

  List<LineChartBarData> get lineBarsData => [lineData1, lineData2];

  LineChartBarData get lineData1 => LineChartBarData(
        isCurved: true,
        color: Colours.colorE282FF,
        barWidth: 3,
        isStrokeCapRound: false,
        dotData: const FlDotData(show: false),
        belowBarData: BarAreaData(show: false),
        spots: state.model.value.teams?.last?.scoreTrend
                ?.asMap()
                .entries
                .map((entry) =>
                    FlSpot(entry.key.toDouble(), (entry.value ?? 0).toDouble()))
                .toList() ??
            [],
      );

  LineChartBarData get lineData2 => LineChartBarData(
        isCurved: true,
        color: Colours.color7732ED,
        barWidth: 3,
        isStrokeCapRound: false,
        dotData: const FlDotData(show: false),
        belowBarData: BarAreaData(show: false),
        spots: state.model.value.teams?.first?.scoreTrend
                ?.asMap()
                .entries
                .map((entry) =>
                    FlSpot(entry.key.toDouble(), (entry.value ?? 0).toDouble()))
                .toList() ??
            [],
      );
}
