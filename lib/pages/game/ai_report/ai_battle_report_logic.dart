import 'package:flutter_common/api.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'dart:developer' as cc;
import 'package:shoot_z/network/api_url.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AiBattleReportLogic extends GetxController {
  var status = 0.obs; // 0 待生成 1 生成中 2 已生成
  var htmlStr = ''.obs;
  final list = [
    {
      'name': 'AI深度解读：',
      'desc': '基于专业比赛数据，瞬间生成专业战报。',
      'icon': WxAssets.images.aiReportIcon1.image(width: 16.w, height: 16.w)
    },
    {
      'name': '虎扑级毒舌点评：',
      'desc': '化身“冷面笑匠”，用最幽默犀利的角度吐槽球队表现、球员高光（&下饭）时刻！',
      'icon': WxAssets.images.aiReportIcon2.image(width: 16.w, height: 16.w)
    },
    {
      'name': '抽象派观赛体验：',
      'desc': '不是干巴巴的数据堆砌，而是让你拍案叫绝的“人类迷惑篮球行为大赏”！',
      'icon': WxAssets.images.aiReportIcon3.image(width: 16.w, height: 16.w)
    },
    {
      'name': '赛后欢乐源泉：',
      'desc': '比赛结束，吐槽开始！分享战报，和球友一起“开会”更有趣！',
      'icon': WxAssets.images.aiReportIcon4.image(width: 16.w, height: 16.w)
    },
  ];
  late String matchId;
  var webController = WebViewController();

  /// 是否正在加载数据
  var init = false.obs;
  @override
  void onInit() {
    super.onInit();
    matchId = Get.arguments;
    late final PlatformWebViewControllerCreationParams params;

    params = const PlatformWebViewControllerCreationParams();
    webController = WebViewController.fromPlatformCreationParams(params);
    // 用于测试的HTML内容
    // _setTestHtmlContent();
    getAIReport(); // 注释掉API调用，使用测试内容
  }

  void _setTestHtmlContent() {
    init.value = true;
    status.value = 2; // 设置为已生成状态
    htmlStr.value = '''
<!DOCTYPE html>
      <html lang="zh-CN">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>斯北图VS融创微电子赛事报告</title>
          <style>
              * {
                  box-sizing: border-box;
                  margin: 0;
                  padding: 0;
                  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
              }
              body {
                  background-color: #f0f2f5;
                  color: #333;
                  line-height: 1.6;
                  padding: 10px;
                  max-width: 100%;
              }
              .container {
                  max-width: 100%;
                  margin: 0 auto;
              }
              header {
                  text-align: center;
                  padding: 1rem 0;
                  background: linear-gradient(135deg, #1e3c72, #2a5298);
                  color: white;
                  border-radius: 10px;
                  margin-bottom: 1.5rem;
                  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
              }

    ''';
  }

  Future<void> getAIReport() async {
    cc.log("Getting AI Report for matchId: $matchId");
    var res = await Api().get(ApiUrl.getAIReport(matchId));
    init.value = true;
    if (res.isSuccessful()) {
      cc.log("API Response status: ${res.data['content']}");
      htmlStr.value = res.data['content'] ?? '';
      webController.loadHtmlString(htmlStr.value);
      // status.value = res.data['status'] as int;
      // cc.log("Current status: ${status.value}");
      // htmlStr.value = res.data['content'] ?? '';
    } else {
      WxLoading.showToast(res.message);
    }
  }
}
