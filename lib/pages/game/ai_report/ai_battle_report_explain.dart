import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:ui_packages/ui_packages.dart';

///我的约战列表
class AiBattleReportExplain extends StatelessWidget {
  const AiBattleReportExplain({super.key});

  @override
  Widget build(BuildContext context) {
    final list = [
      {
        'name': 'AI深度解读：',
        'desc': '基于专业比赛数据，瞬间生成专业战报。',
        'icon': WxAssets.images.aiReportIcon1.image(width: 16.w, height: 16.w)
      },
      {
        'name': '虎扑级毒舌点评：',
        'desc': '化身“冷面笑匠”，用最幽默犀利的角度吐槽球队表现、球员高光（&下饭）时刻！',
        'icon': WxAssets.images.aiReportIcon2.image(width: 16.w, height: 16.w)
      },
      {
        'name': '抽象派观赛体验：',
        'desc': '不是干巴巴的数据堆砌，而是让你拍案叫绝的“人类迷惑篮球行为大赏”！',
        'icon': WxAssets.images.aiReportIcon3.image(width: 16.w, height: 16.w)
      },
      {
        'name': '赛后欢乐源泉：',
        'desc': '比赛结束，吐槽开始！分享战报，和球友一起“开会”更有趣！',
        'icon': WxAssets.images.aiReportIcon4.image(width: 16.w, height: 16.w)
      },
    ];
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI战报'),
      ),
      body: Column(
        children: [
          ListView.builder(
              padding: EdgeInsets.only(top: 0.w),
              itemCount: list.length,
              shrinkWrap: true,
              physics: const ClampingScrollPhysics(),
              itemBuilder: (context, index) {
                return _buildItemCell(
                    list[index]['name'] as String,
                    list[index]['desc'] as String,
                    list[index]['icon'] as Image);
              }),
        ],
      ).marginAll(15.w),
      bottomNavigationBar: InkWell(
        onTap: () {},
        child: Container(
          width: double.infinity,
          height: 50.w,
          alignment: Alignment.center,
          margin: EdgeInsets.only(
              left: 15.w, right: 15.w, bottom: ScreenUtil().bottomBarHeight),
          decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colours.color7732ED, Colours.colorA555EF],
                begin: Alignment.bottomLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25.r)),
          child: Text(
            '生成AI战报',
            style: TextStyles.semiBold14,
          ),
        ),
      ),
    );
  }

  Widget _buildItemCell(String title, String desc, Image icon) {
    return Container(
      margin: EdgeInsets.only(bottom: 15.w),
      padding: EdgeInsets.all(15.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.all(
            Radius.circular(8.r),
          ),
          color: Colours.color191921),
      child: Column(
        children: [
          Row(
            children: [
              icon,
              SizedBox(
                width: 8.w,
              ),
              Text(
                title,
                style: TextStyles.semiBold14,
              )
            ],
          ),
          SizedBox(
            height: 10.w,
          ),
          Text(
            desc,
            style: TextStyles.display14
                .copyWith(color: Colours.colorA8A8BC, height: 1.71),
          )
        ],
      ),
    );
  }
}
