import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab5Mine/squadronBattle/intention_model.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:ui_packages/ui_packages.dart';

///我的约战列表
class AiBattleReportExplain extends StatelessWidget {
  const AiBattleReportExplain({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI战报'),
      ),
      body: Column(
        children: [
          ListView.builder(
              padding: EdgeInsets.only(top: 15.w),
              itemCount: 4,
              shrinkWrap: true,
              physics: const ClampingScrollPhysics(),
              itemBuilder: (context, index) {
                return _buildItemCell(
                    'AI战报', 'AI战报', WxAssets.images.icGameNo.image());
              }),
        ],
      ),
      bottomNavigationBar: InkWell(
        onTap: () {},
        child: Container(
          width: double.infinity,
          height: 50.w,
          alignment: Alignment.center,
          margin: EdgeInsets.only(
              left: 15.w, right: 15.w, bottom: ScreenUtil().bottomBarHeight),
          decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colours.color7732ED, Colours.colorA555EF],
                begin: Alignment.bottomLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25.r)),
          child: Text(
            '生成AI战报',
            style: TextStyles.semiBold14,
          ),
        ),
      ),
    );
  }

  Widget _buildItemCell(String title, String desc, Image icon) {
    return Container(
      padding: EdgeInsets.all(15.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.all(
            Radius.circular(8.r),
          ),
          color: Colours.color191921),
    );
  }
}
