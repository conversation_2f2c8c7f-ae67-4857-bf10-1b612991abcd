import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/pages/game/ai_report/ai_battle_report_logic.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AiBattleReportPage extends StatelessWidget {
  AiBattleReportPage({super.key});
  final logic = Get.put(AiBattleReportLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI战报'),
      ),
      body: Obx(() {
        return !logic.init.value
            ? buildLoad()
            : logic.status.value == 2
                ? WebViewWidget(controller: logic.webController)
                : Column(
                    children: [
                      ListView.builder(
                          padding: EdgeInsets.only(top: 0.w),
                          itemCount: logic.list.length,
                          shrinkWrap: true,
                          physics: const ClampingScrollPhysics(),
                          itemBuilder: (context, index) {
                            return _buildItemCell(
                                logic.list[index]['name'] as String,
                                logic.list[index]['desc'] as String,
                                logic.list[index]['icon'] as Image);
                          }),
                    ],
                  ).marginAll(15.w);
      }),
      bottomNavigationBar: InkWell(
        onTap: () {},
        child: Container(
          width: double.infinity,
          height: 50.w,
          alignment: Alignment.center,
          margin: EdgeInsets.only(
              left: 15.w, right: 15.w, bottom: ScreenUtil().bottomBarHeight),
          decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Colours.color7732ED, Colours.colorA555EF],
                begin: Alignment.bottomLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(25.r)),
          child: Text(
            '生成AI战报',
            style: TextStyles.semiBold14,
          ),
        ),
      ),
    );
  }

  String _sanitizeHtml(String html) {
    // Remove :hover pseudo-class selectors and other unsupported CSS
    return html
        .replaceAll(RegExp(r'[^{}]*:hover[^{}]*\{[^{}]*\}'), '')
        .replaceAll(RegExp(r':hover[^;,{}]*[;,]?'), '')
        .replaceAll(RegExp(r'[^{}]*:focus[^{}]*\{[^{}]*\}'), '')
        .replaceAll(RegExp(r':focus[^;,{}]*[;,]?'), '')
        .replaceAll(RegExp(r'[^{}]*:active[^{}]*\{[^{}]*\}'), '')
        .replaceAll(RegExp(r':active[^;,{}]*[;,]?'), '');
  }

  Widget _buildItemCell(String title, String desc, Image icon) {
    return Container(
      margin: EdgeInsets.only(bottom: 15.w),
      padding: EdgeInsets.all(15.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.all(
            Radius.circular(8.r),
          ),
          color: Colours.color191921),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              icon,
              SizedBox(
                width: 8.w,
              ),
              Text(
                title,
                style: TextStyles.semiBold14,
              )
            ],
          ),
          SizedBox(
            height: 10.w,
          ),
          Text(
            desc,
            style: TextStyles.display14
                .copyWith(color: Colours.colorA8A8BC, height: 1.71),
          )
        ],
      ),
    );
  }
}
