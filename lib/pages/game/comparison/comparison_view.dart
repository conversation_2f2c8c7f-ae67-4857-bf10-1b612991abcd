// ignore_for_file: invalid_use_of_protected_member

import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/pages/game/comparison/comparison_logic.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/utils/utils.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///赛事 比赛中和未开始  预约查看过往数据
///
class ComparisonPage extends StatelessWidget {
  ComparisonPage({super.key});
  final logic = Get.put(ComparisonLogic());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.bg_color,
      // appBar:  MyAppBar(
      //   title: Text(S.current.integral_info1),
      // ),
      body: _teamInfoWidget(context),
      bottomNavigationBar: Obx(() {
        return (logic.isFrist.value)
            ? buildLoad()
            : (logic.comparisonModel.value.teams!.isEmpty == true)
                ? const SizedBox()
                : Container(
                    width: double.infinity,
                    padding: EdgeInsets.only(bottom: 20.w, top: 10.w),
                    decoration: const BoxDecoration(
                        color: Colours.color0F0F16,
                        border: Border(
                            top: BorderSide(
                          width: 1,
                          color: Colours.color282735,
                        ))),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            if (logic.comparisonModel.value.subscribed ==
                                true) {
                              WxLoading.showToast(S.current.game_report6);
                            } else {
                              logic.getMatchesSubscribe();
                            }
                          },
                          child: Container(
                            margin: EdgeInsets.only(left: 16.w),
                            width: 160.w,
                            height: 42.w,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                color: Colours.white,
                                borderRadius: BorderRadius.circular(22.r)),
                            child: Text(
                              S.current.game_report1,
                              style: TextStyles.textSize14
                                  .copyWith(fontSize: 14.sp),
                            ),
                          ),
                        ),
                        GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            if (logic.comparisonModel.value.locked == false) {
                              WxLoading.showToast(S.current.game_report5);
                            } else {
                              AppPage.to(Routes.unlockDataPage,
                                      arguments: {
                                        "type": 0,
                                        "matchId": logic.matchId.value,
                                        "showType": "1",
                                      },
                                      needLogin: true)
                                  .then((onValue) {
                                logic.getdataInfo();
                              });
                            }
                          },
                          child: Container(
                            width: 160.w,
                            height: 42.w,
                            margin: EdgeInsets.only(right: 16.w),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                color: Colours.white,
                                gradient: const LinearGradient(
                                    colors: [
                                      Colours.color7B35ED,
                                      Colours.colorA253EF
                                    ],
                                    begin: Alignment.centerLeft,
                                    end: Alignment.centerRight),
                                borderRadius: BorderRadius.circular(22.r)),
                            child: Text(
                              S.current.game_report2,
                              style: TextStyles.textSize14.copyWith(
                                  color: Colours.white, fontSize: 14.sp),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
      }),
    );
  }

  Widget _teamInfoWidget(BuildContext context) {
    return Obx(() {
      return Stack(
        children: [
          Container(
            width: double.infinity,
            height: 262.w, // 282.w,
            decoration: BoxDecoration(
                image: DecorationImage(
                    image: WxAssets.images.eventReservation1.provider(),
                    fit: BoxFit.fill)),
          ),
          SizedBox(
            width: double.infinity,
            height: double.infinity, // 282.w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  height: 50.w,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(top: ScreenUtil().statusBarHeight),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: 60.w,
                        padding:
                            EdgeInsets.only(left: 8.w, right: 10.w, top: 8.w),
                        child: IconButton(
                            onPressed: () {
                              AppPage.back();
                            },
                            icon: const Icon(
                              Icons.arrow_back_ios,
                              color: Colors.white,
                              size: 20,
                            )),
                      ),
                      Text(
                        S.current.competitions_report,
                        style: TextStyles.textBold16.copyWith(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w400,
                            color: Colours.white),
                      ),
                      Container(
                        width: 60.w,
                        padding: EdgeInsets.only(left: 3.w, right: 10.w),
                      ),
                    ],
                  ),
                ),
                (logic.isFrist.value)
                    ? buildLoad()
                    : (logic.comparisonModel.value.teams!.isEmpty == true)
                        ? Expanded(
                            child: myNoDataView(
                            context,
                            msg: S.current.No_data_available,
                            imagewidget: WxAssets.images.icGameNo
                                .image(width: 105.w, height: 89.w),
                          ))
                        : Expanded(
                            child: SingleChildScrollView(
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 14.w,
                                  ),
                                  SizedBox(
                                    height: 42.w,
                                    width: double.infinity,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        MyImage(
                                          (logic.comparisonModel.value.teams
                                                          ?.length ??
                                                      0) >
                                                  0
                                              ? logic.comparisonModel.value
                                                      .teams?.first?.logo ??
                                                  ""
                                              : "",
                                          width: 42.w,
                                          height: 42.w,
                                          radius: 21.w,
                                          isAssetImage: false,
                                        ),
                                        SizedBox(
                                          width: 100.w,
                                        ),
                                        MyImage(
                                          (logic.comparisonModel.value.teams
                                                          ?.length ??
                                                      0) >
                                                  1
                                              ? logic.comparisonModel.value
                                                      .teams![1]?.logo ??
                                                  ""
                                              : "",
                                          width: 42.w,
                                          height: 42.w,
                                          radius: 21.w,
                                          isAssetImage: false,
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    width: double.infinity,
                                    height: 48.w, // 282.w,
                                    padding: EdgeInsets.only(
                                        top: 18.w, left: 20.w, right: 20.w),
                                    margin: EdgeInsets.only(
                                        left: 28.w, right: 28.w),
                                    decoration: BoxDecoration(
                                        image: DecorationImage(
                                            image: WxAssets
                                                .images.eventReservation3
                                                .provider(),
                                            fit: BoxFit.fill)),
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            (logic.comparisonModel.value.teams
                                                            ?.length ??
                                                        0) >
                                                    0
                                                ? logic
                                                        .comparisonModel
                                                        .value
                                                        .teams
                                                        ?.first
                                                        ?.teamName ??
                                                    ""
                                                : "",
                                            textAlign: TextAlign.center,
                                            style: TextStyles.textBold14
                                                .copyWith(
                                                    fontSize: 13.sp,
                                                    color: Colours.white,
                                                    height: 1,
                                                    fontWeight:
                                                        FontWeight.w400),
                                          ),
                                        ),
                                        Container(
                                          width: 30.w,
                                        ),
                                        Expanded(
                                          child: Text(
                                            ((logic.comparisonModel.value.teams
                                                            ?.length ??
                                                        0) >
                                                    1
                                                ? logic.comparisonModel.value
                                                        .teams![1]?.teamName ??
                                                    ""
                                                : ""),
                                            textAlign: TextAlign.center,
                                            style: TextStyles.textBold14
                                                .copyWith(
                                                    fontSize: 13.sp,
                                                    height: 1,
                                                    color: Colours.color000000,
                                                    fontWeight:
                                                        FontWeight.w400),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                  Container(
                                    width: double.infinity,
                                    height: 117.w, // 282.w,
                                    margin: EdgeInsets.only(
                                        left: 16.w,
                                        right: 16.w,
                                        top: 21.w,
                                        bottom: 15.w),
                                    decoration: BoxDecoration(
                                        image: DecorationImage(
                                            image: WxAssets
                                                .images.eventReservation2
                                                .provider(),
                                            fit: BoxFit.fill)),
                                  ),
                                  _infoWidget1(),
                                  _best(context),
                                  _videoInfoWidget(),
                                  SizedBox(
                                    height: 80.w,
                                  )
                                ],
                              ),
                            ),
                          ),
                // Expanded(
                //   child: _listWidget1(context),
                // ),
              ],
            ),
          )
        ],
      );
    });
  }

  Widget _videoInfoWidget() {
    final leftBest = logic.comparisonModel.value.teams?.first!;
    final rightBest = logic.comparisonModel.value.teams?.last!;
    return Column(
      children: [
        Container(
          width: double.infinity,
          height: 40.w,
          margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 10.w),
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              WxAssets.images.eventReservation7
                  .image(color: Colors.white, width: 59.w, height: 22.w),
              SizedBox(
                width: 4.w,
              ),
              Text(
                S.current.game_report3,
                style: TextStyles.textHint14
                    .copyWith(color: Colours.color5C5C6E, fontSize: 10.sp),
              )
            ],
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                if (leftBest?.videoPath != "" && leftBest?.videoPath != null) {
                  AppPage.to(Routes.videos, arguments: {
                    "videoPath":
                        //   "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/test_fragment/match-highlight_14_59_69_0_1.mp4" ??
                        leftBest?.videoPath ?? "",
                    "teamName": leftBest?.teamName ?? "",
                  });
                } else {
                  WxLoading.showToast(S.current.No_data_available);
                }
              },
              child: Column(
                children: [
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      MyImage(
                        leftBest?.VideoCover ?? "",
                        width: 120.w,
                        height: 90.w,
                        radius: 12.r,
                        errorImage: "error_image_width.png",
                        placeholderImage: "error_image_width.png",
                      ),
                      if (leftBest?.videoPath != "" &&
                          leftBest?.videoPath != null)
                        WxAssets.images.videoPlay
                            .image(width: 24.w, height: 24.w),
                    ],
                  ),
                  SizedBox(
                    height: 12.w,
                  ),
                  Text(
                    leftBest?.teamName ?? "",
                    style: TextStyles.textHint14
                        .copyWith(color: Colours.white, fontSize: 13.sp),
                  )
                ],
              ),
            ),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                if (rightBest?.videoPath != "" &&
                    rightBest?.videoPath != null) {
                  AppPage.to(Routes.videoPath, arguments: {
                    "videoPath":
                        //   "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/test_fragment/match-highlight_14_59_69_0_1.mp4" ??
                        rightBest?.videoPath ?? "",
                    "teamName": rightBest?.teamName ?? "",
                  });
                } else {
                  WxLoading.showToast(S.current.No_data_available);
                }
              },
              child: Column(
                children: [
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      MyImage(
                        rightBest?.VideoCover ?? "",
                        width: 120.w,
                        height: 90.w,
                        radius: 12.r,
                        errorImage: "error_image_width.png",
                        placeholderImage: "error_image_width.png",
                      ),
                      if (rightBest?.videoPath != "" &&
                          rightBest?.videoPath != null)
                        WxAssets.images.videoPlay
                            .image(width: 24.w, height: 24.w),
                    ],
                  ),
                  SizedBox(
                    height: 12.w,
                  ),
                  Text(
                    rightBest?.teamName ?? "",
                    style: TextStyles.textHint14
                        .copyWith(color: Colours.white, fontSize: 13.sp),
                  )
                ],
              ),
            ),
          ],
        ),
        SizedBox(
          height: 24.w,
        ),
      ],
    );
  }

  Widget _infoWidget1() {
    return Column(
      children: [
        Container(
          width: double.infinity,
          height: 40.w,
          margin: EdgeInsets.only(left: 16.w, right: 16.w),
          alignment: Alignment.centerLeft,
          child: WxAssets.images.eventReservation5
              .image(color: Colors.white, width: 59.w, height: 22.w),
        ),
        Container(
          constraints: BoxConstraints(minHeight: 236.w),
          margin: EdgeInsets.only(left: 16.w, right: 16.w),
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: Colours.color191921,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Stack(
            children: [
              ListView.builder(
                  physics: const ClampingScrollPhysics(),
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  itemCount: 5,
                  itemBuilder: (context, index) {
                    var leftTeam = logic.comparisonModel.value.teams?.first!;
                    var rightTeam = logic.comparisonModel.value.teams?.last;
                    // var rightTeam = logic.comparisonModel
                    //     .value.teams?.first;
                    // leftTeam?.winRate = "0.3";
                    // rightTeam?.winRate = "0.4";
                    // leftTeam?.rebound = "15";
                    // rightTeam?.rebound = "1";
                    // leftTeam?.assist = "10";
                    // rightTeam?.assist = "40";
                    // rightTeam?.matchCount = 3;
                    var needCalculate = true;
                    var left = 0.0;
                    var right = 0.0;
                    var title = '';
                    switch (index) {
                      case 0:
                        title = '球队胜率';
                        left = double.parse(
                            leftTeam?.winRate == "" || leftTeam?.winRate == null
                                ? "0.0"
                                : leftTeam?.winRate ?? "0.0");
                        right = double.parse(rightTeam?.winRate == "" ||
                                rightTeam?.winRate == null
                            ? "0.0"
                            : rightTeam?.winRate ?? "0.0");
                        left = double.parse((leftTeam?.winRate == "" ||
                                leftTeam?.winRate == null)
                            ? "0.0"
                            : leftTeam?.winRate ?? "0.0");
                        right = double.parse((rightTeam?.winRate == "" ||
                                rightTeam?.winRate == null)
                            ? "0.0"
                            : rightTeam?.winRate ?? "0.0");
                        needCalculate = false;
                      case 1:
                        title = '场均篮板';
                        left = double.parse((leftTeam?.rebound == "" ||
                                leftTeam?.rebound == null)
                            ? "0.0"
                            : leftTeam?.rebound ?? "0.0");
                        right = double.parse((rightTeam?.rebound == "" ||
                                rightTeam?.rebound == null)
                            ? "0.0"
                            : rightTeam?.rebound ?? "0.0");
                      case 2:
                        title = '场均助攻';

                        left = double.parse(
                            (leftTeam?.assist == "" || leftTeam?.assist == null)
                                ? "0.0"
                                : leftTeam?.assist ?? "0.0");
                        right = double.parse((rightTeam?.assist == "" ||
                                rightTeam?.assist == null)
                            ? "0.0"
                            : rightTeam?.assist ?? "0.0");
                      case 3:
                        title = '投篮命中率';
                        left = double.parse(!(leftTeam?.shotRate == null ||
                                leftTeam?.shotRate == "")
                            ? leftTeam!.shotRate!
                            : '0');
                        right = double.parse(!(rightTeam?.shotRate == null ||
                                rightTeam?.shotRate == "")
                            ? rightTeam!.shotRate!
                            : '0');
                        needCalculate = false;

                      case 4:
                        left = double.parse(!(leftTeam?.threeRate == null ||
                                leftTeam?.threeRate == "")
                            ? leftTeam!.threeRate!
                            : '0');
                        right = double.parse(!(rightTeam?.threeRate == null ||
                                rightTeam?.threeRate == "")
                            ? rightTeam!.threeRate!
                            : '0');
                        title = '三分命中率';
                        needCalculate = false;
                    }
                    return comparisonItemsWidget(
                      left: left,
                      right: right,
                      title: title,
                      needCalculate: needCalculate,
                      showLeft: (leftTeam?.matchCount ?? 0) > 0,
                      showRight: (rightTeam?.matchCount ?? 0) > 0,
                    ).marginOnly(top: 9.w, bottom: 9.w);
                  }),
              if ((logic.comparisonModel.value.teams?.first?.matchCount ?? 0) <=
                  0)
                Positioned(
                  left: 16.w,
                  top: 65.w,
                  child: Container(
                    height: 85.w,
                    alignment: Alignment.centerLeft,
                    child: WxAssets.images.noData2
                        .image(width: 81.w, height: 85.w),
                  ),
                ),
              if ((logic.comparisonModel.value.teams?.last?.matchCount ?? 0) <=
                  0)
                Positioned(
                  right: 16.w,
                  top: 65.w,
                  child: Container(
                    height: 85.w,
                    alignment: Alignment.centerLeft,
                    child: WxAssets.images.noData2
                        .image(width: 81.w, height: 85.w),
                  ),
                )
            ],
          ),
        ),
      ],
    );
  }

  Widget comparisonItemsWidget(
      {double left = 0,
      double right = 0,
      String title = "",
      bool needCalculate = true,
      bool showLeft = true,
      bool showRight = true}) {
    final leftText = needCalculate
        ? left.toInt().toString()
        : Utils.formatToPercentage(left);
    final rightText = needCalculate
        ? right.toInt().toString()
        : Utils.formatToPercentage(right);
    var l = left;
    var r = right;
    if (needCalculate && (left + right) > 0) {
      l = left / (left + right);
      r = 1 - l;
    }
    var leftColor = Colours.color7732ED;
    var rightColor = Colours.white;
    if (r != l) {
      leftColor = l > r || (!showRight)
          ? Colours.color7732ED
          : Colours.color7732ED.withOpacity(0.2);
      rightColor =
          l > r || (!showLeft) ? Colours.white.withOpacity(0.1) : Colours.white;
    }
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox(
              width: 70.w,
              child: Text(
                showLeft ? leftText : "",
                style: TextStyles.medium.copyWith(fontSize: 14.sp),
              ),
            ),
            Text(
              title,
              style: TextStyles.regular.copyWith(
                  fontSize: 12.sp,
                  //  color: Colours.color5C5C6E,
                  fontWeight: FontWeight.w600,
                  foreground: Paint()
                    ..shader = const LinearGradient(
                            colors: [Colours.colorC5FE94, Colours.colorF3FC5C])
                        .createShader(const Rect.fromLTWH(
                      0,
                      0,
                      300,
                      50,
                    ))),
            ),
            SizedBox(
              width: 70.w,
              child: Text(
                showRight ? rightText : "",
                textAlign: TextAlign.right,
                style: TextStyles.medium.copyWith(fontSize: 14.sp),
              ),
            ),
          ],
        ),
        SizedBox(
          height: 12.w,
        ),
        Row(
          children: [
            Expanded(
              child: !showLeft
                  ? const SizedBox()
                  : LayoutBuilder(
                      builder: (context, constraints) => Container(
                        height: 6.w,
                        decoration: BoxDecoration(
                          color: Colours.color22222D,
                          borderRadius: BorderRadius.circular(3.w),
                        ),
                        child: Stack(
                          children: [
                            Positioned(
                                top: 0,
                                bottom: 0,
                                right: 0,
                                left: (1 - l) * constraints.maxWidth,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: leftColor,
                                    borderRadius: BorderRadius.horizontal(
                                        left: Radius.circular(3.w)),
                                  ),
                                )),
                          ],
                        ),
                      ),
                    ),
            ),
            Expanded(
              child: !showRight
                  ? const SizedBox()
                  : LayoutBuilder(
                      builder: (context, constraints) => Container(
                        height: 6.w,
                        decoration: BoxDecoration(
                          color: Colours.color22222D,
                          borderRadius: BorderRadius.circular(3.w),
                        ),
                        child: Stack(
                          children: [
                            Positioned(
                                top: 0,
                                bottom: 0,
                                left: 0,
                                right: constraints.maxWidth -
                                    r * constraints.maxWidth,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: rightColor,
                                    borderRadius: BorderRadius.horizontal(
                                        right: Radius.circular(3.w)),
                                  ),
                                )),
                          ],
                        ),
                      ),
                    ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _best(BuildContext context) {
    final leftBest = logic.comparisonModel.value.teams?.first!;
    final rightBest = logic.comparisonModel.value.teams?.last!;
    return leftBest?.scoreKing == null &&
            leftBest?.reboundKing == null &&
            leftBest?.assistKing == null &&
            rightBest?.scoreKing == null &&
            rightBest?.reboundKing == null &&
            rightBest?.assistKing == null
        ? const SizedBox()
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                height: 40.w,
                margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 10.w),
                alignment: Alignment.centerLeft,
                child: WxAssets.images.eventReservation6
                    .image(color: Colors.white, width: 59.w, height: 22.w),
              ),
              Container(
                padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 20.w),
                margin: EdgeInsets.only(left: 16.w, right: 16.w),
                decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3), // 阴影颜色
                      offset: const Offset(0, 4), // 阴影偏移 (水平, 垂直)
                      blurRadius: 10, // 阴影模糊半径
                      spreadRadius: 1, // 阴影扩散半径
                    ),
                  ],
                ),
                child: ListView.builder(
                    physics: const ClampingScrollPhysics(),
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    itemCount: 3,
                    itemBuilder: (context, index) {
                      return Obx(() => _bestListItem(context, index));
                    }),
              ),
            ],
          );
  }

  Widget _bestListItem(BuildContext context, int index) {
    final leftBest = logic.comparisonModel.value.teams?.first!;
    final rightBest = logic.comparisonModel.value.teams?.last!;
    final left = index == 0
        ? leftBest?.scoreKing
        : (index == 1 ? leftBest?.reboundKing : leftBest?.assistKing);
    final right = index == 0
        ? rightBest?.scoreKing
        : (index == 1 ? rightBest?.reboundKing : rightBest?.assistKing);
    final leftS = left?.score ?? 0;
    final rightS = right?.score ?? 0;

    final text = index == 0 ? '得分' : (index == 1 ? '篮板' : '助攻');
    return Row(
      children: [
        Expanded(
            child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          // onTap: () => logic.unlockPlayer(
          //     state.model.teams.first.teamId, left.playerId, left.locking),
          child: Row(
            children: [
              MyImage(
                left?.playerPhoto ?? "",
                width: 50.w,
                height: 60.w,
                fit: BoxFit.cover,
                radius: 4.r,
                isAssetImage: false,
              ),
              SizedBox(
                width: 10.w,
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    leftS.toString(),
                    style: GoogleFonts.oswald(
                        fontSize: 20.sp,
                        fontWeight: AppFontWeight.medium(),
                        color: Colours.white,
                        height: 1),
                  ),
                  SizedBox(
                    height: 18.w,
                  ),
                  Text(
                    '#${left?.playerNumber ?? ""}',
                    style:
                        TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                  )
                ],
              ),
            ],
          ),
        )),
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  width: 11.w,
                  height: (leftS >= rightS ? 1 : leftS / rightS) * 32.w,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: leftS < rightS
                            ? [Colours.color7B35ED, Colours.colorA253EF]
                            : [Colours.colorC5FE94, Colours.colorF3FC5C],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight),
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(2)),
                  ),
                ),
                SizedBox(
                  width: 6.w,
                ),
                Container(
                  width: 11.w,
                  height: (leftS > rightS ? rightS / leftS : 1) * 32.w,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: rightS < leftS
                            ? [Colours.color7B35ED, Colours.colorA253EF]
                            : [Colours.colorC5FE94, Colours.colorF3FC5C],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight),
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(2)),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 10.w,
            ),
            Text(
              text,
              style: TextStyles.regular.copyWith(fontSize: 12.sp),
            ),
          ],
        ),
        Expanded(
            child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  rightS.toString(),
                  style: GoogleFonts.oswald(
                      fontSize: 20.sp,
                      fontWeight: AppFontWeight.medium(),
                      color: Colours.white,
                      height: 1),
                ),
                SizedBox(
                  height: 18.w,
                ),
                Text(
                  '#${right?.playerNumber}',
                  style:
                      TextStyles.regular.copyWith(color: Colours.color5C5C6E),
                )
              ],
            ),
            SizedBox(
              width: 10.w,
            ),
            MyImage(
              right?.playerPhoto ?? "",
              width: 50.w,
              height: 60.w,
              fit: BoxFit.cover,
              radius: 4.r,
              isAssetImage: false,
            ),
          ],
        )),
      ],
    ).paddingOnly(bottom: 20.w);
  }
}
