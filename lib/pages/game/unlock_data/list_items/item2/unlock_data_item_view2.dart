import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/game_coupons_model.dart';
import 'package:shoot_z/pages/game/details/models/match_pay_info_model.dart';
import 'package:shoot_z/pages/game/unlock_data/list_items/item2/unlock_data_item_logic2.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:ui_packages/ui_packages.dart';

///比赛 解锁数据 ->整队解锁
class UnlockDataItemPage2 extends StatelessWidget {
  UnlockDataItemPage2({super.key});

  final logic = Get.put(UnlockDataItemLogic2());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return Column(
          children: [
            Container(
              margin: EdgeInsets.only(left: 15.w, right: 15.w),
              padding: EdgeInsets.only(
                  left: 20.w, right: 15.w, top: 30.w, bottom: 20.w),
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image: WxAssets.images.unlockBg2.provider(),
                      fit: BoxFit.fill)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      WxAssets.images.imgUnlock1
                          .image(width: 14.w, height: 14.w),
                      SizedBox(
                        width: 7.w,
                      ),
                      Text(
                        S.current.unlock_team_tips1,
                        style: TextStyles.regular.copyWith(
                            fontWeight: FontWeight.w600, fontSize: 14.sp),
                      ),
                      // const Spacer(),
                      // Container(
                      //   padding: EdgeInsets.only(
                      //       left: 9.w, right: 9.w, bottom: 6.w, top: 6.w),
                      //   decoration: BoxDecoration(
                      //       gradient: const LinearGradient(colors: [
                      //         Colours.color7732ED,
                      //         Colours.colorA555EF
                      //       ]),
                      //       borderRadius: BorderRadius.circular(30.r)),
                      //   child: Row(
                      //     mainAxisSize: MainAxisSize.min,
                      //     children: [
                      //       WxAssets.images.imgUnlock3
                      //           .image(width: 11.w, height: 11.w),
                      //       SizedBox(
                      //         width: 4.w,
                      //       ),
                      //       Text(
                      //         S.current.view_examples,
                      //         style: TextStyles.regular.copyWith(
                      //             fontWeight: FontWeight.w600, fontSize: 10.sp),
                      //       ),
                      //     ],
                      //   ),
                      // ),
                    ],
                  ),
                  SizedBox(
                    height: 12.w,
                  ),
                  Text(
                    S.current.unlock_all_tips2,
                    style: TextStyles.regular.copyWith(
                        fontWeight: FontWeight.w400,
                        fontSize: 12.sp,
                        height: 1.4,
                        color: Colours.color9393A5),
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  Row(
                    children: [
                      WxAssets.images.imgUnlock2
                          .image(width: 14.w, height: 14.w),
                      SizedBox(
                        width: 7.w,
                      ),
                      Text(
                        S.current.unlock_team_tips2,
                        style: TextStyles.regular.copyWith(
                            fontWeight: FontWeight.w600, fontSize: 14.sp),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 12.w,
                  ),
                  Text(
                    S.current.unlock_team_tips3,
                    style: TextStyles.regular.copyWith(
                        fontWeight: FontWeight.w400,
                        fontSize: 12.sp,
                        height: 1.4,
                        color: Colours.color9393A5),
                  ),
                ],
              ),
            ),
            Container(
              width: double.infinity,
              margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 20.w),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: Colours.color191921,
                  borderRadius: BorderRadius.circular(16.r)),
              child: Column(
                children: [
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      //选择球队
                      getOpenTeamDialog(context);
                    },
                    child: Container(
                      width: double.infinity,
                      height: 60.w,
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          color: Colours.color191921,
                          borderRadius: BorderRadius.circular(16.r)),
                      child: Row(
                        children: [
                          Text(
                            S.current.unlock_team_tips4,
                            style: TextStyles.display16.copyWith(
                                fontSize: 14.sp,
                                color: Colours.color9393A5,
                                fontWeight: FontWeight.w400),
                          ),
                          const Spacer(),
                          Text(
                            logic.matchPayInfoModelTeams.value.teamName ??
                                S.current.unlock_team_tips9,
                            style: TextStyles.display16.copyWith(
                                fontSize: 14.sp,
                                color: logic.matchPayInfoModelTeams.value
                                            .teamName ==
                                        null
                                    ? Colours.color9393A5
                                    : Colours.white,
                                fontWeight: FontWeight.w400),
                          ),
                          SizedBox(
                            width: 2.w,
                          ),
                          WxAssets.images.icArrowRight.image(
                            width: 14.w,
                            height: 14.w,
                            color: Colours.color9393A5,
                          )
                        ],
                      ),
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      //跳转到赛事优惠券列表
                      AppPage.to(Routes.competitionCouponsPage, arguments: {
                        "matchCouponType": logic.matchCouponType,
                        "money":
                            logic.matchPayInfoModel.value?.matchDiscountPrice ??
                                logic.matchPayInfoModel.value?.matchPrice ??
                                0.0
                      }).then((onValue) {
                        if (onValue != null) {
                          log("gameCouponsModel=${jsonEncode(onValue["coupons"])}");
                          logic.gameCouponsModel.value =
                              onValue["coupons"] as GameCouponsModel;
                          logic.checkCoupon(logic.gameCouponsModel.value);
                        }
                      });
                    },
                    child: Container(
                      width: double.infinity,
                      height: 60.w,
                      padding: EdgeInsets.symmetric(horizontal: 15.w),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          color: Colours.color191921,
                          borderRadius: BorderRadius.circular(16.r)),
                      child: Row(
                        children: [
                          Text(
                            S.current.coupon,
                            style: TextStyles.display16.copyWith(
                                fontSize: 14.sp,
                                color: Colours.color9393A5,
                                fontWeight: FontWeight.w400),
                          ),
                          const Spacer(),
                          if (logic.maximumReducedAmount.value == "")
                            Text(
                              S.current.option_coupon1,
                              style: TextStyles.display16.copyWith(
                                  fontSize: 12.sp,
                                  color: Colours.color9393A5,
                                  fontWeight: FontWeight.w400),
                            ),
                          if (logic.maximumReducedAmount.value != "" &&
                              (logic.gameCouponsModel.value.id ?? "") == "")
                            Container(
                              padding: EdgeInsets.only(
                                  left: 10.w,
                                  right: 10.w,
                                  top: 2.w,
                                  bottom: 2.w),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(24.r),
                                  gradient: const LinearGradient(
                                      colors: [
                                        Color(0xffFFECC1),
                                        Color(0xffE7CEFF),
                                        Color(0xffD1EAFF)
                                      ],
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight)),
                              child: Text(
                                S.current.option_coupon3(
                                    logic.maximumReducedAmount.value),
                                style: TextStyles.display16.copyWith(
                                    fontSize: 11.sp,
                                    color: Colours.color333333,
                                    fontWeight: FontWeight.w400),
                              ),
                            ),
                          if (logic.maximumReducedAmount.value != "" &&
                              (logic.gameCouponsModel.value.id ?? "") != "")
                            Container(
                              padding: EdgeInsets.only(
                                  left: 10.w,
                                  right: 10.w,
                                  top: 2.w,
                                  bottom: 2.w),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(24.r),
                                  gradient: const LinearGradient(
                                      colors: [
                                        Color(0xffFFECC1),
                                        Color(0xffE7CEFF),
                                        Color(0xffD1EAFF)
                                      ],
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight)),
                              child: Text(
                                S.current.unlock_team_tips5,
                                style: TextStyles.display16.copyWith(
                                    fontSize: 11.sp,
                                    color: Colours.color333333,
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                          if (logic.maximumReducedAmount.value != "" &&
                              (logic.gameCouponsModel.value.id ?? "") != "")
                            Text(
                              "\t\t-￥${logic.decreaseMoney.value}",
                              style: TextStyles.display16.copyWith(
                                  fontSize: 14.sp,
                                  color: Colours.colorFFF280,
                                  fontWeight: FontWeight.w600),
                            ),
                          SizedBox(
                            width: 2.w,
                          ),
                          WxAssets.images.icArrowRight.image(
                            width: 14.w,
                            height: 14.w,
                            color: Colours.color9393A5,
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 20.w,
            ),
            Row(
              children: [
                SizedBox(
                  width: 15.w,
                ),
                Padding(
                  padding: EdgeInsets.only(bottom: 1.w),
                  child: WxAssets.images.tips.image(width: 12.w, height: 12.w),
                ),
                SizedBox(
                  width: 4.w,
                ),
                Text(
                  S.current.unlock_team_tips6(
                      logic.matchPayInfoModelTeams.value.teamName ?? ""),
                  style: TextStyles.regular
                      .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
                )
              ],
            )
          ],
        );
      }),
      bottomNavigationBar: Obx(() {
        return Container(
          height: 50.w,
          width: double.infinity,
          alignment: Alignment.center,
          margin:
              EdgeInsets.only(left: 20.w, right: 20.w, bottom: 25.w, top: 10.w),
          padding: EdgeInsets.only(
            left: 20.w,
          ),
          decoration: BoxDecoration(
            color: Colours.color282735,
            borderRadius: BorderRadius.all(Radius.circular(28.r)),
            gradient: const LinearGradient(
              colors: [Colours.color7732ED, Colours.colorA555EF],
              begin: Alignment.bottomLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                    text: "￥", //type	integer1 次数券；2 金额抵扣券；3 折扣券
                    style: TextStyle(
                        color: Colours.white,
                        fontSize: 14.sp,
                        height: 1,
                        fontWeight: FontWeight.w600),
                    children: <InlineSpan>[
                      TextSpan(
                          text: "${logic.preferentialMoney.value}\t",
                          style: TextStyle(
                              color: Colours.white,
                              fontSize: 22.sp,
                              height: 1,
                              fontWeight: FontWeight.normal)),
                      if (logic.matchPayInfoModel.value?.teamDiscountPrice !=
                          logic.matchPayInfoModel.value?.teamPrice)
                        TextSpan(
                            text: S.current.unlock_team_tips12(
                                "${logic.matchPayInfoModel.value?.teamPrice ?? ""}"),
                            style: TextStyle(
                                color: Colours.colorD5B6F8,
                                fontSize: 12.sp,
                                height: 1,
                                fontWeight: FontWeight.normal,
                                decoration: TextDecoration.lineThrough,
                                decorationStyle: TextDecorationStyle.solid,
                                decorationColor: Colours.colorD5B6F8,
                                decorationThickness: 1)),
                      TextSpan(
                          text:
                              "\t\t${S.current.already_discounted(logic.allReadyDeMoney.value)}", //decreaseMoney
                          style: TextStyle(
                              color: Colours.colorD5B6F8,
                              fontSize: 12.sp,
                              height: 1,
                              fontWeight: FontWeight.normal)),
                    ]),
              ),
              const Spacer(),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  logic.payOrder();
                },
                child: Container(
                  height: 42.w,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(right: 4.w),
                  padding: EdgeInsets.only(left: 20.w, right: 20.w),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(21.r),
                    color: Colours.white,
                  ),
                  child: Text(
                    S.current.unlock_now,
                    style: TextStyles.display16.copyWith(
                        fontSize: 14.sp,
                        color: Colours.color333333,
                        fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  //选择解锁球队
  void getOpenTeamDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            height: 372.w,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                Container(
                    width: double.infinity,
                    padding: EdgeInsets.only(top: 18.w, bottom: 20.w),
                    alignment: Alignment.center,
                    child: Text(
                      S.current.unlock_team_tips9,
                      style: TextStyles.medium.copyWith(fontSize: 16.sp),
                    )),
                GridView.builder(
                    scrollDirection: Axis.vertical,
                    // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                    shrinkWrap: true,
                    physics:
                        const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 15,
                      mainAxisSpacing: 15,
                      childAspectRatio: 1,
                    ),
                    padding: EdgeInsets.only(bottom: 10.w, top: 10.w),
                    itemCount:
                        (logic.matchPayInfoModel.value?.teams?.length ?? 0) > 2
                            ? 2
                            : logic.matchPayInfoModel.value?.teams?.length,
                    itemBuilder: (context, index) {
                      return Obx(() {
                        return GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            if (logic.matchPayInfoModel.value?.teams![index] !=
                                    null &&
                                ((logic.matchPayInfoModel.value?.teams?[index]
                                            ?.locked ??
                                        1) !=
                                    0)) {
                              logic.matchPayInfoModelTeams.value = logic
                                      .matchPayInfoModel.value?.teams?[index] ??
                                  MatchPayInfoModelTeams();
                            }
                          },
                          child: Container(
                            decoration: BoxDecoration(
                                color: Colours.color22222D,
                                borderRadius: BorderRadius.circular(16.r),
                                border:
                                    logic.matchPayInfoModelTeams.value.teamId ==
                                            logic.matchPayInfoModel.value
                                                ?.teams![index]?.teamId
                                        ? Border.all(
                                            width: 1,
                                            color: Colours.colorA44EFF)
                                        : Border.all(
                                            width: 1,
                                            color: Colours.color22222D)),
                            child: Column(
                              children: [
                                SizedBox(
                                  height: 36.w,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      //1锁住  0解锁
                                      if ((logic.matchPayInfoModel.value
                                                  ?.teams?[index]?.locked ??
                                              1) ==
                                          0)
                                        Container(
                                          width: 36.w,
                                          height: 36.w,
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                              color: Colours.color5D5D6E,
                                              borderRadius: BorderRadius.only(
                                                topRight: Radius.circular(16.r),
                                                bottomLeft:
                                                    Radius.circular(16.r),
                                              )),
                                          child: MyImage(
                                            'ic_game_unlock.png',
                                            //  holderImg: "home/index/df_banner_top",
                                            width: 18.w,
                                            height: 18.w,
                                            isAssetImage: true,
                                            // errorImg: "home/index/df_banner_top"
                                            radius: 8.r,
                                          ),
                                        )
                                    ],
                                  ),
                                ),
                                Transform.translate(
                                  offset: Offset(0, -6.w),
                                  child: MyImage(
                                    logic.matchPayInfoModel.value?.teams?[index]
                                            ?.logo ??
                                        '',
                                    //  holderImg: "home/index/df_banner_top",
                                    fit: BoxFit.fill,
                                    width: 66.w,
                                    height: 66.w,
                                    isAssetImage: false,
                                    bgColor: Colors.transparent,
                                    // errorImg: "home/index/df_banner_top"
                                    radius: 4.r,
                                  ),
                                ),
                                SizedBox(
                                  height: 14.w,
                                ),
                                Center(
                                  child: Text(
                                    logic.matchPayInfoModel.value?.teams?[index]
                                            ?.teamName ??
                                        '',
                                    maxLines: 1,
                                    textAlign: TextAlign.center,
                                    style: TextStyles.regular.copyWith(
                                        fontSize: 14.sp,
                                        color: ((logic
                                                        .matchPayInfoModel
                                                        .value
                                                        ?.teams?[index]
                                                        ?.locked ??
                                                    1) ==
                                                0)
                                            ? Colours.color9393A5
                                            : Colours.white), //color9393A5
                                  ),
                                )
                              ],
                            ),
                          ),
                        );
                      });
                    }),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.only(bottom: 25.w, top: 10.w),
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () async {
                      Get.back();
                    },
                    child: Container(
                      height: 46.w,
                      width: double.infinity,
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(left: 20.w, right: 20.w),
                      decoration: BoxDecoration(
                        color: Colours.color282735,
                        borderRadius: BorderRadius.all(Radius.circular(28.r)),
                        gradient: const LinearGradient(
                          colors: [Colours.color7732ED, Colours.colorA555EF],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: Text(
                        S.current.sure,
                        style: TextStyles.display16.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        });
      },
    );
  }
}
