import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common/wx_loading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/game_coupons_model.dart';
import 'package:shoot_z/pages/game/details/models/match_pay_info_model.dart';
import 'package:shoot_z/pages/game/unlock_data/list_items/item3/unlock_data_item_logic3.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:shoot_z/widgets/MyImage.dart';
import 'package:shoot_z/widgets/view.dart';
import 'package:ui_packages/ui_packages.dart';

///比赛 解锁数据 ->单人解锁
class UnlockDataItemPage3 extends StatelessWidget {
  UnlockDataItemPage3({super.key});

  final logic = Get.put(UnlockDataItemLogic3());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return SingleChildScrollView(
          child: Column(
            children: [
              Container(
                margin: EdgeInsets.only(left: 15.w, right: 15.w),
                padding: EdgeInsets.only(
                    left: 20.w, right: 15.w, top: 30.w, bottom: 20.w),
                decoration: BoxDecoration(
                    image: DecorationImage(
                        image: WxAssets.images.unlockBg3.provider(),
                        fit: BoxFit.fill)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        WxAssets.images.imgUnlock1
                            .image(width: 14.w, height: 14.w),
                        SizedBox(
                          width: 7.w,
                        ),
                        Text(
                          S.current.unlock_person_tips1,
                          style: TextStyles.regular.copyWith(
                              fontWeight: FontWeight.w600, fontSize: 14.sp),
                        ),
                        // const Spacer(),
                        // Container(
                        //   padding: EdgeInsets.only(
                        //       left: 9.w, right: 9.w, bottom: 6.w, top: 6.w),
                        //   decoration: BoxDecoration(
                        //       gradient: const LinearGradient(colors: [
                        //         Colours.color7732ED,
                        //         Colours.colorA555EF
                        //       ]),
                        //       borderRadius: BorderRadius.circular(30.r)),
                        //   child: Row(
                        //     mainAxisSize: MainAxisSize.min,
                        //     children: [
                        //       WxAssets.images.imgUnlock3
                        //           .image(width: 11.w, height: 11.w),
                        //       SizedBox(
                        //         width: 4.w,
                        //       ),
                        //       Text(
                        //         S.current.view_examples,
                        //         style: TextStyles.regular.copyWith(
                        //             fontWeight: FontWeight.w600,
                        //             fontSize: 10.sp),
                        //       ),
                        //     ],
                        //   ),
                        // ),
                      ],
                    ),
                    SizedBox(
                      height: 12.w,
                    ),
                    Text(
                      S.current.unlock_all_tips2,
                      style: TextStyles.regular.copyWith(
                          fontWeight: FontWeight.w400,
                          fontSize: 12.sp,
                          height: 1.4,
                          color: Colours.color9393A5),
                    ),
                    SizedBox(
                      height: 20.w,
                    ),
                    Row(
                      children: [
                        WxAssets.images.imgUnlock2
                            .image(width: 14.w, height: 14.w),
                        SizedBox(
                          width: 7.w,
                        ),
                        Text(
                          S.current.unlock_person_tips2,
                          style: TextStyles.regular.copyWith(
                              fontWeight: FontWeight.w600, fontSize: 14.sp),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 12.w,
                    ),
                    Text(
                      S.current.unlock_person_tips3,
                      style: TextStyles.regular.copyWith(
                          fontWeight: FontWeight.w400,
                          fontSize: 12.sp,
                          height: 1.4,
                          color: Colours.color9393A5),
                    ),
                  ],
                ),
              ),
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 20.w),
                height: 60.w,
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.circular(16.r)),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 130.w,
                      child: Text(
                        S.current.unlock_person_tips4,
                        style: TextStyles.display16.copyWith(
                            fontSize: 12.sp,
                            color: Colours.white, //color9393A5
                            fontWeight: FontWeight.w400),
                      ),
                    ),
                    const Spacer(),
                    Row(
                      children: List.generate(4, (index) {
                        return MyImage(
                          index <
                                  (logic.matchPayInfoModel.value
                                          ?.unlockedPlayers?.length ??
                                      0)
                              ? logic.matchPayInfoModel.value
                                      ?.unlockedPlayers![index]?.playerAvatar ??
                                  logic.matchPayInfoModel.value
                                      ?.unlockedPlayers![index]?.photo ??
                                  ""
                              : "",
                          width: 34.w,
                          height: 34.w,
                          radius: 17.r,
                          errorImage: "img_player_head.png",
                          placeholderImage: "img_player_head.png",
                          margin: EdgeInsets.only(left: 8.w),
                        );
                      }),
                    )
                  ],
                ),
              ),
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.circular(16.r)),
                child: Column(
                  children: [
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        //解锁球员弹窗选择
                        getOpenTeamDialog(context);
                      },
                      child: Container(
                        width: double.infinity,
                        height: 76.w,
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            color: Colours.color191921,
                            borderRadius: BorderRadius.circular(16.r)),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    S.current.unlock_person_tips5,
                                    style: TextStyles.display16.copyWith(
                                        fontSize: 14.sp,
                                        color: Colours.color9393A5,
                                        fontWeight: FontWeight.w400),
                                  ),
                                  SizedBox(
                                    height: 10.w,
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      logic.isAgreeUnlock.value =
                                          !logic.isAgreeUnlock.value;
                                    },
                                    child: Row(
                                      children: [
                                        logic.isAgreeUnlock.value
                                            ? WxAssets.images.chooseYes2.image(
                                                width: 12.w, height: 12.w)
                                            : WxAssets.images.choiceNo.image(
                                                color: Colours.colorD5B6F8,
                                                width: 12.w,
                                                height: 12.w),
                                        SizedBox(
                                          width: 5.w,
                                        ),
                                        Text(
                                          S.current.unlock_person_tips7,
                                          style: TextStyles.display16.copyWith(
                                              fontSize: 11.sp,
                                              color: Colours.colorD5B6F8,
                                              fontWeight: FontWeight.w400),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            ),
                            if ((logic.matchPayInfoModelUnlockedPlayers.value
                                        .photo ??
                                    "") !=
                                "")
                              MyImage(
                                logic.matchPayInfoModelUnlockedPlayers.value
                                        .photo ??
                                    "",
                                width: 40.w,
                                height: 47.w,
                                radius: 4.r,
                                margin: EdgeInsets.only(left: 8.w),
                              ),
                            SizedBox(
                              width: 8.w,
                            ),
                            Text(
                              (logic.matchPayInfoModelUnlockedPlayers.value
                                              .number ??
                                          "") ==
                                      ""
                                  ? S.current.unlock_person_tips6
                                  : S.current.player_number(logic
                                          .matchPayInfoModelUnlockedPlayers
                                          .value
                                          .number ??
                                      ""),
                              style: TextStyles.display16.copyWith(
                                  fontSize: 14.sp,
                                  color: (logic.matchPayInfoModelUnlockedPlayers
                                                  .value.number ??
                                              "") ==
                                          ""
                                      ? Colours.color9393A5
                                      : Colours.white,
                                  fontWeight: FontWeight.w400),
                            ),
                            SizedBox(
                              width: 2.w,
                            ),
                            WxAssets.images.icArrowRight.image(
                              width: 14.w,
                              height: 14.w,
                              color: Colours.color9393A5,
                            )
                          ],
                        ),
                      ),
                    ),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        //跳转到赛事优惠券列表
                        AppPage.to(Routes.competitionCouponsPage, arguments: {
                          "matchCouponType": logic.matchCouponType,
                          "money": logic.matchPayInfoModel.value
                                  ?.matchDiscountPrice ??
                              logic.matchPayInfoModel.value?.matchPrice ??
                              0.0
                        }).then((onValue) {
                          if (onValue != null) {
                            log("gameCouponsModel=${jsonEncode(onValue["coupons"])}");
                            logic.gameCouponsModel.value =
                                onValue["coupons"] as GameCouponsModel;
                            logic.checkCoupon(logic.gameCouponsModel.value);
                          }
                        });
                      },
                      child: Container(
                        width: double.infinity,
                        height: 60.w,
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            color: Colours.color191921,
                            borderRadius: BorderRadius.circular(16.r)),
                        child: Row(
                          children: [
                            Text(
                              S.current.coupon,
                              style: TextStyles.display16.copyWith(
                                  fontSize: 14.sp,
                                  color: Colours.color9393A5,
                                  fontWeight: FontWeight.w400),
                            ),
                            const Spacer(),
                            if (logic.maximumReducedAmount.value == "")
                              Text(
                                S.current.option_coupon1,
                                style: TextStyles.display16.copyWith(
                                    fontSize: 12.sp,
                                    color: Colours.color9393A5,
                                    fontWeight: FontWeight.w400),
                              ),
                            if (logic.maximumReducedAmount.value != "" &&
                                (logic.gameCouponsModel.value.id ?? "") == "")
                              Container(
                                padding: EdgeInsets.only(
                                    left: 10.w,
                                    right: 10.w,
                                    top: 2.w,
                                    bottom: 2.w),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(24.r),
                                    gradient: const LinearGradient(
                                        colors: [
                                          Color(0xffFFECC1),
                                          Color(0xffE7CEFF),
                                          Color(0xffD1EAFF)
                                        ],
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight)),
                                child: Text(
                                  S.current.option_coupon3(
                                      logic.maximumReducedAmount.value),
                                  style: TextStyles.display16.copyWith(
                                      fontSize: 11.sp,
                                      color: Colours.color333333,
                                      fontWeight: FontWeight.w400),
                                ),
                              ),
                            if (logic.maximumReducedAmount.value != "" &&
                                (logic.gameCouponsModel.value.id ?? "") != "")
                              Container(
                                padding: EdgeInsets.only(
                                    left: 10.w,
                                    right: 10.w,
                                    top: 2.w,
                                    bottom: 2.w),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(24.r),
                                    gradient: const LinearGradient(
                                        colors: [
                                          Color(0xffFFECC1),
                                          Color(0xffE7CEFF),
                                          Color(0xffD1EAFF)
                                        ],
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight)),
                                child: Text(
                                  S.current.unlock_team_tips5,
                                  style: TextStyles.display16.copyWith(
                                      fontSize: 11.sp,
                                      color: Colours.color333333,
                                      fontWeight: FontWeight.w600),
                                ),
                              ),
                            if (logic.maximumReducedAmount.value != "" &&
                                (logic.gameCouponsModel.value.id ?? "") != "")
                              Text(
                                "\t\t-￥${logic.decreaseMoney.value}",
                                style: TextStyles.display16.copyWith(
                                    fontSize: 14.sp,
                                    color: Colours.colorFFF280,
                                    fontWeight: FontWeight.w600),
                              ),
                            SizedBox(
                              width: 2.w,
                            ),
                            WxAssets.images.icArrowRight.image(
                              width: 14.w,
                              height: 14.w,
                              color: Colours.color9393A5,
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (!(UserManager.instance.userInfo.value?.isSVip ?? false))
                Container(
                  width: double.infinity,
                  margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.w),
                  padding: EdgeInsets.all(15.w),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      color: Colours.color191921,
                      borderRadius: BorderRadius.circular(16.r)),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          WxAssets.images.svipAdv1
                              .image(width: 16.w, height: 16.w),
                          SizedBox(
                            width: 3.w,
                          ),
                          WxAssets.images.svipAdv2
                              .image(width: 113.w, height: 14.w),
                          const Spacer(),
                          RichText(
                            textAlign: TextAlign.right,
                            text: TextSpan(
                                text: S.current.svip_adv_tips1,
                                style: TextStyle(
                                    color: Colours.color9393A5,
                                    fontSize: 12.sp,
                                    height: 1,
                                    fontWeight: FontWeight.w400),
                                children: <InlineSpan>[
                                  TextSpan(
                                      text: S.current.svip_adv_tips2,
                                      style: TextStyle(
                                          color: Colours.colorFFF280,
                                          fontSize: 12.sp,
                                          height: 1,
                                          fontWeight: FontWeight.w400)),
                                  TextSpan(
                                      text: S.current.svip_adv_tips3,
                                      style: TextStyle(
                                          color: Colours.color9393A5,
                                          fontSize: 12.sp,
                                          height: 1,
                                          fontWeight: FontWeight.w400)),
                                  TextSpan(
                                      text: logic.moneyPayx4.value,
                                      style: TextStyle(
                                          color: Colours.colorFFF280,
                                          fontSize: 12.sp,
                                          height: 1,
                                          fontWeight: FontWeight.w400)),
                                  TextSpan(
                                      text: S.current.yuan,
                                      style: TextStyle(
                                          color: Colours.color9393A5,
                                          fontSize: 12.sp,
                                          height: 1,
                                          fontWeight: FontWeight.w400)),
                                ]),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 10.w,
                      ),
                      GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            //开通svip
                            AppPage.to(Routes.vipPage,
                                arguments: true, needLogin: true);
                          },
                          child: WxAssets.images.svipAdv3
                              .image(width: 315.w, height: 90.w)),
                    ],
                  ),
                ),
              SizedBox(
                height: 20.w,
              ),
              Row(
                children: [
                  SizedBox(
                    width: 15.w,
                  ),
                  Padding(
                    padding: EdgeInsets.only(bottom: 1.w),
                    child:
                        WxAssets.images.tips.image(width: 12.w, height: 12.w),
                  ),
                  SizedBox(
                    width: 4.w,
                  ),
                  Text(
                    S.current.unlock_team_tips6(logic
                            .matchPayInfoModel.value?.teams
                            ?.where((number) =>
                                number?.teamId ==
                                logic.matchPayInfoModelUnlockedPlayers.value
                                    .teamId)
                            .firstOrNull
                            ?.teamName ??
                        ""),
                    style: TextStyles.regular
                        .copyWith(fontSize: 12.sp, color: Colours.color5C5C6E),
                  )
                ],
              )
            ],
          ),
        );
      }),
      bottomNavigationBar: Obx(() {
        return (!(UserManager.instance.userInfo.value?.isSVip ?? false)) &&
                ((logic.gameCouponsModel.value.id ?? "") == "")
            ? Container(
                height: 50.w,
                width: double.infinity,
                alignment: Alignment.center,
                margin: EdgeInsets.only(
                    left: 20.w, right: 20.w, bottom: 25.w, top: 10.w),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        if (logic.matchPayInfoModel.value?.boundMatchPlayerId ==
                                "0" ||
                            logic.matchPayInfoModel.value?.boundMatchPlayerId ==
                                "" ||
                            logic.matchPayInfoModel.value?.boundMatchPlayerId ==
                                null) {
                          logic.payOrder();
                        } else {
                          getMyDialog(
                            S.current.dialog_title,
                            S.current.sure,
                            content: S.current.unlock_person_tips9,
                            () {
                              AppPage.back();
                              logic.payOrder();
                            },
                            isShowClose: false,
                            btnIsHorizontal: true,
                            btnText2: S.current.cancel,
                            onPressed2: () {
                              AppPage.back();
                            },
                          );
                        }
                      },
                      child: Container(
                        width: 130.w,
                        height: 50.w,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                            color: Colours.color2C2C39,
                            borderRadius:
                                BorderRadius.all(Radius.circular(25.r))),
                        child: Text(
                          S.current.unlock_data_tips1((logic.moneyPay)),
                          // "${UserManager.instance.userInfo.value?.userId ?? ""}",
                          style: TextStyles.regular.copyWith(fontSize: 15.sp),
                        ),
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          //开通svip
                          AppPage.to(Routes.vipPage,
                              arguments: true, needLogin: true);
                        },
                        child: Container(
                          width: double.infinity,
                          height: 50.w,
                          margin: EdgeInsets.only(left: 17.w),
                          alignment: Alignment.center,
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          decoration: BoxDecoration(
                            color: Colours.color2C2C39,
                            borderRadius: BorderRadius.all(
                              Radius.circular(25.r),
                            ),
                            gradient: const LinearGradient(
                              colors: [
                                Colours.color7732ED,
                                Colours.colorA555EF
                              ],
                              begin: Alignment.bottomLeft,
                              end: Alignment.bottomRight,
                            ),
                          ),
                          child: Text(
                            S.current.unlock_data_tips2,
                            style: TextStyles.regular.copyWith(fontSize: 15.sp),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            : Container(
                height: 50.w,
                width: double.infinity,
                alignment: Alignment.center,
                margin: EdgeInsets.only(
                    left: 20.w, right: 20.w, bottom: 25.w, top: 10.w),
                padding: EdgeInsets.only(
                  left: 20.w,
                ),
                decoration: BoxDecoration(
                  color: Colours.color282735,
                  borderRadius: BorderRadius.all(Radius.circular(28.r)),
                  gradient: const LinearGradient(
                    colors: [Colours.color7732ED, Colours.colorA555EF],
                    begin: Alignment.bottomLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                          text: "￥", //type	integer1 次数券；2 金额抵扣券；3 折扣券
                          style: TextStyle(
                              color: Colours.white,
                              fontSize: 14.sp,
                              height: 1,
                              fontWeight: FontWeight.w600),
                          children: <InlineSpan>[
                            TextSpan(
                                text: "${logic.preferentialMoney.value}\t",
                                style: TextStyle(
                                    color: Colours.white,
                                    fontSize: 22.sp,
                                    height: 1,
                                    fontWeight: FontWeight.w600)),
                            TextSpan(
                                text: S.current.already_discounted(
                                    logic.decreaseMoney.value),
                                style: TextStyle(
                                    color: Colours.colorD5B6F8,
                                    fontSize: 12.sp,
                                    height: 1,
                                    fontWeight: FontWeight.normal)),
                          ]),
                    ),
                    const Spacer(),
                    GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        if (logic.matchPayInfoModel.value?.boundMatchPlayerId ==
                                "0" ||
                            logic.matchPayInfoModel.value?.boundMatchPlayerId ==
                                "" ||
                            logic.matchPayInfoModel.value?.boundMatchPlayerId ==
                                null) {
                          logic.payOrder();
                        } else {
                          getMyDialog(
                            S.current.dialog_title,
                            S.current.sure,
                            content: S.current.unlock_person_tips9,
                            () {
                              AppPage.back();
                              logic.payOrder();
                            },
                            isShowClose: false,
                            btnIsHorizontal: true,
                            btnText2: S.current.cancel,
                            onPressed2: () {
                              AppPage.back();
                            },
                          );
                        }
                      },
                      child: Container(
                        height: 42.w,
                        alignment: Alignment.center,
                        margin: EdgeInsets.only(right: 4.w),
                        padding: EdgeInsets.only(left: 20.w, right: 20.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(21.r),
                          color: Colours.white,
                        ),
                        child: Text(
                          S.current.unlock_now,
                          style: TextStyles.display16.copyWith(
                              fontSize: 14.sp,
                              color: Colours.color333333,
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  ],
                ),
              );
      }),
    );
  }

  //选择解锁球员
  void getOpenTeamDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: const Color(0x70000000),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)), // 圆角
      ),
      isScrollControlled: true, // 允许更大的高度
      builder: (context) {
        return Obx(() {
          return Container(
            width: double.infinity,
            height: 632,
            padding: EdgeInsets.only(left: 20.w, right: 20.w),
            decoration: BoxDecoration(
                color: Colours.color191921,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Container(
                    width: 38.w,
                    height: 4,
                    margin: EdgeInsets.only(top: 8.w),
                    decoration: BoxDecoration(
                        color: Colours.color10D8D8D8,
                        borderRadius: BorderRadius.circular(4.r)),
                  ),
                ),
                Container(
                    width: double.infinity,
                    padding: EdgeInsets.only(top: 18.w, bottom: 20.w),
                    alignment: Alignment.center,
                    child: Text(
                      S.current.unlock_person_tips8,
                      style: TextStyles.medium.copyWith(fontSize: 16.sp),
                    )),
                Container(
                  width: double.infinity,
                  height: 40.w,
                  alignment: Alignment.centerLeft,
                  color: Colors.transparent,
                  child: Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            logic.switchTab(0);
                          },
                          child: Container(
                            width: double.infinity,
                            height: 40.w,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(5.r),
                                  bottomLeft: Radius.circular(5.r),
                                ),
                                color: logic.tabbarIndex.value == 0
                                    ? Colours.color9A46FF
                                    : Colours.white),
                            child: Text(
                              logic.matchPayInfoModel.value?.teams?.first
                                      ?.teamName ??
                                  "",
                              style: TextStyles.regular.copyWith(
                                  color: logic.tabbarIndex.value == 0
                                      ? Colours.white
                                      : Colours.color000000),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            logic.switchTab(1);
                          },
                          child: Container(
                            width: double.infinity,
                            height: 40.w,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.only(
                                  topRight: Radius.circular(5.r),
                                  bottomRight: Radius.circular(5.r),
                                ),
                                color: logic.tabbarIndex.value == 1
                                    ? Colours.color9A46FF
                                    : Colours.white),
                            child: Text(
                              logic.matchPayInfoModel.value?.teams?.last
                                      ?.teamName ??
                                  "",
                              style: TextStyles.regular.copyWith(
                                  color: logic.tabbarIndex.value == 1
                                      ? Colours.white
                                      : Colours.color000000),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 20.w,
                ),
                Expanded(
                  child: TabBarView(controller: logic.tabController, children: [
                    tabviewItemWidget(logic.dataPlayerList1),
                    tabviewItemWidget(logic.dataPlayerList2),
                  ]),
                ),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.only(bottom: 25.w, top: 10.w),
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () async {
                      if ((logic.matchPayInfoModelUnlockedPlayers.value
                                  .playerId ??
                              "") ==
                          "") {
                        WxLoading.showToast(S.current.unlock_person_tips6);
                        return;
                      }
                      Get.back();
                    },
                    child: Container(
                      height: 46.w,
                      width: double.infinity,
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(left: 20.w, right: 20.w),
                      decoration: BoxDecoration(
                        color: Colours.color282735,
                        borderRadius: BorderRadius.all(Radius.circular(28.r)),
                        gradient: const LinearGradient(
                          colors: [Colours.color7732ED, Colours.colorA555EF],
                          begin: Alignment.bottomLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: Text(
                        S.current.sure,
                        style: TextStyles.display16.copyWith(fontSize: 16.sp),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  height: 20.w,
                ),
              ],
            ),
          );
        });
      },
    );
  }

  SizedBox tabviewItemWidget(
      RxList<MatchPayInfoModelLockedPlayers> dataPlayerList) {
    return SizedBox(
      height: 580.w,
      child: ListView(
        shrinkWrap: true,
        children: [
          GridView.builder(
              scrollDirection: Axis.vertical,
              // padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
              shrinkWrap: true,
              physics:
                  const NeverScrollableScrollPhysics(), // const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 75 / 130,
              ),
              padding: EdgeInsets.only(bottom: 70.w, top: 0.w),
              itemCount: dataPlayerList.length,
              itemBuilder: (context, index) {
                return Obx(() {
                  return GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      if (dataPlayerList[index].locked != "0") {
                        logic.matchPayInfoModelUnlockedPlayers.value =
                            dataPlayerList[index];
                      }
                    },
                    child: Column(
                      children: [
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            MyImage(
                              dataPlayerList[index].photo ?? '',
                              //  holderImg: "home/index/df_banner_top",
                              fit: BoxFit.fill,
                              width: 75.w,
                              height: 100.w,
                              isAssetImage: false,
                              // errorImg: "home/index/df_banner_top"
                              radius: 4.r,
                            ),
                            if (logic.matchPayInfoModelUnlockedPlayers.value
                                    .playerId ==
                                dataPlayerList[index].playerId)
                              Container(
                                width: 75.w,
                                height: 100.w,
                                alignment: Alignment.center,
                                decoration: const BoxDecoration(
                                    color: Colours.color80000000),
                                child: Container(
                                  width: 20.w,
                                  height: 20.w,
                                  margin: EdgeInsets.only(
                                      right: 8.w, bottom: 3.w, top: 8.w),
                                  child: const Icon(
                                    Icons.check,
                                    color: Colours.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                            if (dataPlayerList[index].locked == "0")
                              Container(
                                width: 75.w,
                                height: 100.w,
                                alignment: Alignment.center,
                                decoration: const BoxDecoration(
                                    color: Colours.color80000000),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    WxAssets.images.icGameUnlock
                                        .image(width: 18.w, height: 18.w),
                                    SizedBox(
                                      height: 8.w,
                                    ),
                                    Text(
                                      S.current.unlocked,
                                      style: TextStyles.regular
                                          .copyWith(fontSize: 10.sp),
                                    )
                                  ],
                                ),
                              )
                          ],
                        ),
                        SizedBox(
                          height: 10.w,
                        ),
                        Center(
                          child: Text(
                            S.current.player_number(
                                dataPlayerList[index].number ?? ""),
                            maxLines: 1,
                            textAlign: TextAlign.center,
                            style: TextStyles.regular.copyWith(
                                fontSize: 14.sp, color: Colours.color9393A5),
                          ),
                        )
                      ],
                    ),
                  );
                });
              }),
        ],
      ),
    );
  }
}
