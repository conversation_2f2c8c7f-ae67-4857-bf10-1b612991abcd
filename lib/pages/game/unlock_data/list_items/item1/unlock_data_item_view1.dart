import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/generated/l10n.dart';
import 'package:shoot_z/network/model/game_coupons_model.dart';
import 'package:shoot_z/pages/game/unlock_data/list_items/item1/unlock_data_item_logic1.dart';
import 'package:shoot_z/routes/app.dart';
import 'package:shoot_z/routes/route.dart';
import 'package:ui_packages/ui_packages.dart';

///比赛 解锁数据 ->整场解锁
class UnlockDataItemPage1 extends StatelessWidget {
  UnlockDataItemPage1({super.key});

  final logic = Get.put(UnlockDataItemLogic1());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return Column(
          children: [
            Container(
              margin: EdgeInsets.only(left: 15.w, right: 15.w),
              padding: EdgeInsets.only(
                  left: 20.w, right: 15.w, top: 30.w, bottom: 20.w),
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image: WxAssets.images.imgUnlockBg.provider(),
                      fit: BoxFit.fill)),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      WxAssets.images.imgUnlock1
                          .image(width: 14.w, height: 14.w),
                      SizedBox(
                        width: 7.w,
                      ),
                      Text(
                        S.current.unlock_all_tips1,
                        style: TextStyles.regular.copyWith(
                            fontWeight: FontWeight.w600, fontSize: 14.sp),
                      ),
                      // const Spacer(),
                      // Container(
                      //   padding: EdgeInsets.only(
                      //       left: 9.w, right: 9.w, bottom: 6.w, top: 6.w),
                      //   decoration: BoxDecoration(
                      //       gradient: const LinearGradient(colors: [
                      //         Colours.color7732ED,
                      //         Colours.colorA555EF
                      //       ]),
                      //       borderRadius: BorderRadius.circular(30.r)),
                      //   child: Row(
                      //     mainAxisSize: MainAxisSize.min,
                      //     children: [
                      //       WxAssets.images.imgUnlock3
                      //           .image(width: 11.w, height: 11.w),
                      //       SizedBox(
                      //         width: 4.w,
                      //       ),
                      //       Text(
                      //         S.current.view_examples,
                      //         style: TextStyles.regular.copyWith(
                      //             fontWeight: FontWeight.w600, fontSize: 10.sp),
                      //       ),
                      //     ],
                      //   ),
                      // ),
                    ],
                  ),
                  SizedBox(
                    height: 12.w,
                  ),
                  Text(
                    S.current.unlock_all_tips2,
                    style: TextStyles.regular.copyWith(
                        fontWeight: FontWeight.w400,
                        fontSize: 12.sp,
                        height: 1.4,
                        color: Colours.color9393A5),
                  ),
                  SizedBox(
                    height: 20.w,
                  ),
                  Row(
                    children: [
                      WxAssets.images.imgUnlock2
                          .image(width: 14.w, height: 14.w),
                      SizedBox(
                        width: 7.w,
                      ),
                      Text(
                        S.current.unlock_all_tips3,
                        style: TextStyles.regular.copyWith(
                            fontWeight: FontWeight.w600, fontSize: 14.sp),
                      )
                    ],
                  ),
                  SizedBox(
                    height: 12.w,
                  ),
                  Text(
                    S.current.unlock_all_tips4,
                    style: TextStyles.regular.copyWith(
                        fontWeight: FontWeight.w400,
                        fontSize: 12.sp,
                        height: 1.4,
                        color: Colours.color9393A5),
                  ),
                ],
              ),
            ),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                //跳转到赛事优惠券列表
                AppPage.to(Routes.competitionCouponsPage, arguments: {
                  "matchCouponType": logic.matchCouponType,
                  "money": logic.matchPayInfoModel.value?.matchDiscountPrice ??
                      logic.matchPayInfoModel.value?.matchPrice ??
                      0.0
                }).then((onValue) {
                  if (onValue != null) {
                    log("gameCouponsModel=${jsonEncode(onValue["coupons"])}");
                    logic.gameCouponsModel.value =
                        onValue["coupons"] as GameCouponsModel;
                    logic.checkCoupon(logic.gameCouponsModel.value);
                  }
                });
              },
              child: Container(
                width: double.infinity,
                height: 60.w,
                margin: EdgeInsets.only(left: 15.w, right: 15.w, top: 20.w),
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: Colours.color191921,
                    borderRadius: BorderRadius.circular(16.r)),
                child: Row(
                  children: [
                    Text(
                      S.current.coupon,
                      style: TextStyles.display16.copyWith(
                          fontSize: 14.sp,
                          color: Colours.color9393A5,
                          fontWeight: FontWeight.w400),
                    ),
                    const Spacer(),
                    if (logic.maximumReducedAmount.value == "")
                      Text(
                        S.current.option_coupon1,
                        style: TextStyles.display16.copyWith(
                            fontSize: 14.sp,
                            color: Colours.color9393A5,
                            fontWeight: FontWeight.w400),
                      ),
                    if (logic.maximumReducedAmount.value != "" &&
                        (logic.gameCouponsModel.value.id ?? "") == "")
                      Container(
                        padding: EdgeInsets.only(
                            left: 10.w, right: 10.w, top: 2.w, bottom: 2.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.r),
                            gradient: const LinearGradient(
                                colors: [
                                  Color(0xffFFECC1),
                                  Color(0xffE7CEFF),
                                  Color(0xffD1EAFF)
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight)),
                        child: Text(
                          S.current
                              .option_coupon3(logic.maximumReducedAmount.value),
                          style: TextStyles.display16.copyWith(
                              fontSize: 11.sp,
                              color: Colours.color333333,
                              fontWeight: FontWeight.w400),
                        ),
                      ),
                    if (logic.maximumReducedAmount.value != "" &&
                        (logic.gameCouponsModel.value.id ?? "") != "")
                      Container(
                        padding: EdgeInsets.only(
                            left: 10.w, right: 10.w, top: 2.w, bottom: 2.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(24.r),
                            gradient: const LinearGradient(
                                colors: [
                                  Color(0xffFFECC1),
                                  Color(0xffE7CEFF),
                                  Color(0xffD1EAFF)
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight)),
                        child: Text(
                          S.current.unlock_team_tips5,
                          style: TextStyles.display16.copyWith(
                              fontSize: 11.sp,
                              color: Colours.color333333,
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                    if (logic.maximumReducedAmount.value != "" &&
                        (logic.gameCouponsModel.value.id ?? "") != "")
                      Text(
                        "\t\t-￥${logic.decreaseMoney.value}",
                        style: TextStyles.display16.copyWith(
                            fontSize: 14.sp,
                            color: Colours.colorFFF280,
                            fontWeight: FontWeight.w400),
                      ),
                    SizedBox(
                      width: 2.w,
                    ),
                    WxAssets.images.icArrowRight.image(
                      width: 14.w,
                      height: 14.w,
                      color: Colours.color9393A5,
                    )
                  ],
                ),
              ),
            )
          ],
        );
      }),
      bottomNavigationBar: Obx(() {
        return Container(
          height: 50.w,
          width: double.infinity,
          alignment: Alignment.center,
          margin:
              EdgeInsets.only(left: 20.w, right: 20.w, bottom: 25.w, top: 10.w),
          padding: EdgeInsets.only(
            left: 20.w,
          ),
          decoration: BoxDecoration(
            color: Colours.color282735,
            borderRadius: BorderRadius.all(Radius.circular(28.r)),
            gradient: const LinearGradient(
              colors: [Colours.color7732ED, Colours.colorA555EF],
              begin: Alignment.bottomLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                    text: "￥", //type	integer1 次数券；2 金额抵扣券；3 折扣券
                    style: TextStyle(
                        color: Colours.white,
                        fontSize: 14.sp,
                        height: 1,
                        fontWeight: FontWeight.w600),
                    children: <InlineSpan>[
                      TextSpan(
                          text: "${logic.preferentialMoney.value}\t",
                          style: TextStyle(
                              color: Colours.white,
                              fontSize: 22.sp,
                              height: 1,
                              fontWeight: FontWeight.w600)),
                      if (logic.matchPayInfoModel.value?.matchDiscountPrice !=
                          logic.matchPayInfoModel.value?.matchPrice)
                        TextSpan(
                            text: S.current.unlock_team_tips12(
                                "${logic.matchPayInfoModel.value?.matchPrice ?? ""}"),
                            style: TextStyle(
                                color: Colours.colorD5B6F8,
                                fontSize: 12.sp,
                                height: 1,
                                fontWeight: FontWeight.normal,
                                decoration: TextDecoration.lineThrough,
                                decorationStyle: TextDecorationStyle.solid,
                                decorationColor: Colours.colorD5B6F8,
                                decorationThickness: 1)),
                      TextSpan(
                          text:
                              "\t\t${S.current.already_discounted(logic.allReadyDeMoney.value)}", //decreaseMoney
                          style: TextStyle(
                              color: Colours.colorD5B6F8,
                              fontSize: 12.sp,
                              height: 1,
                              fontWeight: FontWeight.normal)),
                    ]),
              ),
              const Spacer(),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  logic.payOrder();
                },
                child: Container(
                  height: 42.w,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(right: 4.w),
                  padding: EdgeInsets.only(left: 20.w, right: 20.w),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(21.r),
                    color: Colours.white,
                  ),
                  child: Text(
                    S.current.unlock_now,
                    style: TextStyles.display16.copyWith(
                        fontSize: 14.sp,
                        color: Colours.color333333,
                        fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}
