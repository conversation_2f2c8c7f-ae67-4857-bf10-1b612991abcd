import 'package:get/get.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item1/tab_home_item_logic1.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item2/tab_home_item_logic2.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item3/tab_home_item_logic3.dart';
import 'package:shoot_z/pages/tab1Home/tab_items/item4/tab_home_item_logic4.dart';

import 'package:shoot_z/pages/tab1Home/tab_home_logic.dart';

class TabHomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TabHomeLogic());
    Get.lazyPut(() => TabHomeItemLogic1());
    Get.lazyPut(() => TabHomeItemLogic2());
    Get.lazyPut(() => TabHomeItemLogic3());
    Get.lazyPut(() => TabHomeItemLogic4());
  }
}
