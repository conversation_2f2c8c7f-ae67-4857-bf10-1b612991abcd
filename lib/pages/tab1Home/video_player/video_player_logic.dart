import 'dart:developer';

import 'package:better_player/better_player.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:shoot_z/network/model/featured_list_model.dart';
import 'package:shoot_z/pages/login/user.dart';
import 'package:shoot_z/utils/myShareH5.dart';

class VideoPlayerLogic extends GetxController {
  late BetterPlayerController betterPlayerController;
  BetterPlayerDataSource? _betterPlayerDataSource;

  var type = "0".obs; //0集锦  普通集锦  1半场集锦
  var featuredListModel = FeaturedListModel().obs; //标题1
  var isShare = "0".obs; //是否分享 1修改
  var isUpdate = "0".obs; //是否修改 0不修改
  var isHorizontal = true.obs;
  var aspectRatio = (9 / 16).obs;
  var isFrist = true.obs;
  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments.containsKey('type')) {
      type.value = Get.arguments['type'] ?? '';
    }
    if (Get.arguments != null &&
        Get.arguments.containsKey('featuredListModel')) {
      featuredListModel.value = Get.arguments['featuredListModel'] ?? '';
    }
    if (featuredListModel.value.videoUrl?.isNotEmpty ?? false) {
      _initializePlayer();
    }
  }

  Future<void> _initializePlayer() async {
    isFrist.value = true;
    // 竖屏视频示例URL
    var verticalVideoUrl = featuredListModel.value.videoUrl ?? "";
    // "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/shootz_sys/kol/20250519-1205351753695263976.mp4";
    //"https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/shootz_sys/kol/%E4%BA%8C%E4%BB%A3%E5%9C%BA%E6%99%AF%E4%B8%801753772645596.mp4";

    _betterPlayerDataSource = BetterPlayerDataSource(
      BetterPlayerDataSourceType.network,
      cacheConfiguration: const BetterPlayerCacheConfiguration(
        useCache: true,
        maxCacheFileSize: 50 * 1024 * 1024,
        maxCacheSize: 50 * 1024 * 1024,
        preCacheSize: 10 * 1024 * 1024,
      ),
      verticalVideoUrl,
      //"https://vod.pule.com/6c992c3bvodcq1500003583/af54817b5285890813370412301/f0.mp4",
      // "https://vd4.bdstatic.com/mda-nf7hhj7wqge0a2s8/mda-nf7hhj7wqge0a2s8.mp4", //  videoUrl,
      liveStream: false,
    );
    betterPlayerController = BetterPlayerController(
      BetterPlayerConfiguration(
          autoPlay: true,
          looping: false,
          fit: BoxFit.contain,
          translations: [BetterPlayerTranslations.chinese()],
          deviceOrientationsAfterFullScreen: [
            DeviceOrientation.portraitDown,
            DeviceOrientation.portraitUp
          ],
          controlsConfiguration: const BetterPlayerControlsConfiguration(
            playerTheme: BetterPlayerTheme.material,
            enableSkips: false,
            showControlsOnInitialize: false, // 初始化时显示控制栏
          ),
          aspectRatio: 9 / 16),
      betterPlayerDataSource: _betterPlayerDataSource,
    );
    // 监听视频初始化事件
    betterPlayerController.addEventsListener((event) {
      if (event.betterPlayerEventType == BetterPlayerEventType.initialized) {
        // 检测视频方向
        final videoWidth =
            betterPlayerController.videoPlayerController!.value.size?.width ??
                1;
        final videoHeight =
            betterPlayerController.videoPlayerController!.value.size?.height ??
                0;
        log("betterPlayerController2=$videoWidth $videoHeight");
        isHorizontal.value = videoHeight > videoWidth ? false : true;
        aspectRatio.value = videoHeight > videoWidth ? 9 / 16 : 16 / 9;
        refresh();
        if (isFrist.value) {
          isFrist.value = false;
        }
      }
    });
    //   betterPlayerController.setControlsEnabled(false);
  }

  @override
  void onClose() {
    super.onClose();
    betterPlayerController.dispose();
  }

  void share() async {
    switch (type.value) {
      case "0": //普通半场
      case "1": //半场集锦
        MyShareH5.getShareH5(ShareVideosId(
            videosId: (featuredListModel.value.id ?? "")
                .toString())); // //0普通集锦  1半场集锦
        break;
    }
  }

  void downloadAndSaveVideo() {
    // Utils.downloadAndSaveToPhotoAlbum(videoPath.value);
  }

  // void showDeleteDialog() {
  //   Get.dialog(CustomAlertDialog(
  //     title: S.current.confirm_deletion,
  //     content: S.current.video_removal_tips,
  //     onPressed: () async {
  //       AppPage.back();
  //       getDeleteVideo();
  //     },
  //   ));
  // }

  // Future<void> getDeleteVideo() async {
  //   Map<String, dynamic> param2 = {"id": videoId.value};
  //   var url = await ApiUrl.getDeleteVideo(videoId.value);
  //   var res = await Api().delete(url, data: param2);
  //   log("getDeleteVideo=${videoId.value}-${res.data}");
  //   if (res.isSuccessful()) {
  //     AppPage.back(result: true);
  //   } else {
  //     WxLoading.showToast(res.message);
  //   }
  // }
}
