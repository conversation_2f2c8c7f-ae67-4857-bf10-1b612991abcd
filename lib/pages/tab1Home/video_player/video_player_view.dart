// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get.dart';
// import 'package:shoot_z/gen/assets.gen.dart';
// import 'package:shoot_z/generated/l10n.dart';
// import 'package:shoot_z/pages/game/comparison/video_path/video_path_logic.dart';
// import 'package:shoot_z/routes/app.dart';
// import 'package:shoot_z/widgets/MyAppBar.dart';
// import 'package:ui_packages/ui_packages.dart';

// class VideoPlayerPage extends StatefulWidget {
//   const VideoPlayerPage({super.key});

//   @override
//   State<VideoPlayerPage> createState() => _HighlightsVideoPageState();
// }

// class _HighlightsVideoPageState extends State<VideoPlayerPage> {
//   final VideoPathLogic logic = Get.put(VideoPathLogic());
//   @override
//   void initState() {
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Obx(() {
//       return Scaffold(
//           appBar: logic.isShowShareUpdate.value == "1"
//               ? MyAppBar(
//                   title: Text(logic.teamName.value),
//                 )
//               : null,
//           body: SafeArea(
//             child: Column(
//               children: [
//                 if (logic.isShowShareUpdate.value != "1") _title(context),
//                 SizedBox(
//                   height: 47.w,
//                 ),
//                 _video(context),
//                 SizedBox(
//                   height: 47.w,
//                 ),
//               ],
//             ),
//           ));
//     });
//   }

//   Widget _title(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         Padding(
//           padding: const EdgeInsets.only(top: 20, left: 20, right: 20),
//           child: Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Flexible(
//                   child: Text(
//                 "",
//                 style: TextStyles.titleSemiBold16.copyWith(fontSize: 20.sp),
//                 overflow: TextOverflow.ellipsis,
//                 maxLines: 1,
//               )),
//               SizedBox(
//                 width: 10.w,
//               ),
//               GestureDetector(
//                   onTap: () => AppPage.back(),
//                   child:
//                       WxAssets.images.icClose.image(width: 20.w, height: 20.w)),
//             ],
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _video(BuildContext context) {
//     // if (!logic.videoPlayerController.value.isInitialized) {
//     //   return const Center(child: CircularProgressIndicator());
//     // }

//     return Expanded(
//       child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
//         if (logic.isShowShareUpdate.value != "1")
//           Padding(
//               padding: EdgeInsets.symmetric(horizontal: 30.w),
//               child: Text(
//                 logic.teamName.value,
//                 style: TextStyles.display14.copyWith(
//                     color: Colours.color9393A5,
//                     fontWeight: FontWeight.w400,
//                     fontSize: 16.sp),
//                 maxLines: 1,
//               )),
//         if (logic.isShowShareUpdate.value != "1")
//           SizedBox(
//             height: 20.w,
//           ),
//         // SizedBox(
//         //   width: double.infinity,
//         //   height: ScreenUtil().screenWidth / 375 * 211,
//         //   child: AspectRatio(
//         //     aspectRatio: 375 / 211, // 宽高比
//         //     child: BetterPlayer(controller: logic.betterPlayerController),
//         //   ),
//         // ),
//         if (logic.isShowShareUpdate.value == "1") _action(context),
//       ]),
//     );
//   }

//   Widget _action(BuildContext context) {
//     return Padding(
//       padding: EdgeInsets.only(bottom: 26, top: 100.w),
//       child: Wrap(
//           spacing: 40.w,
//           children: List.generate(3, (index) {
//             return GestureDetector(
//               behavior: HitTestBehavior.translucent,
//               onTap: () {
//                 switch (index) {
//                   case 0:
//                     logic.share();
//                     break;
//                   case 1:
//                     logic.downloadAndSaveVideo();
//                     break;
//                   case 2:
//                     logic.showDeleteDialog();
//                     break;
//                 }
//               },
//               child: SizedBox(
//                 width: 80.w,
//                 height: 70.w,
//                 child: Column(
//                   children: [
//                     index == 0
//                         ? WxAssets.images.icShare
//                             .image(width: 21.w, height: 21.w)
//                         : index == 1
//                             ? WxAssets.images.icDownload
//                                 .image(width: 21.w, height: 21.w)
//                             : WxAssets.images.icDelete
//                                 .image(width: 21.w, height: 21.w),
//                     SizedBox(
//                       height: 15.w,
//                     ),
//                     Text(
//                       index == 0
//                           ? S.current.video_share
//                           : index == 1
//                               ? S.current.video_download
//                               : S.current.video_delete,
//                       style: TextStyles.display14.copyWith(
//                           color: Colours.white,
//                           fontWeight: FontWeight.w400,
//                           fontSize: 12.sp),
//                       maxLines: 1,
//                     )
//                   ],
//                 ),
//               ),
//             );
//           })),
//     );
//   }
// }
// import 'package:flutter/material.dart';
// import 'package:better_player/better_player.dart';
// import 'package:get/get.dart';
// import 'package:shoot_z/pages/game/comparison/video_path/video_path_logic.dart';

// class VideoPlayerPage extends StatefulWidget {
//   const VideoPlayerPage({super.key});

//   @override
//   State<VideoPlayerPage> createState() => _BetterPlayerOnlyState();
// }

// class _BetterPlayerOnlyState extends State<VideoPlayerPage> {
//   late BetterPlayerController _betterPlayerController;
//   final logic = Get.put(VideoPathLogic());
//   @override
//   void initState() {
//     super.initState();
//     _initializePlayer();
//   }

//   void _initializePlayer() {
//     final dataSource = BetterPlayerDataSource(
//         BetterPlayerDataSourceType.network,
//         "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/shootz_sys/kol/20250519-1205351753695263976.mp4"
//         // "https://uni-shootz-1308047407.cos.ap-guangzhou.myqcloud.com/shootz_sys/kol/%E4%BA%8C%E4%BB%A3%E5%9C%BA%E6%99%AF%E4%B8%801753772645596.mp4"
//         );

//     _betterPlayerController = BetterPlayerController(
//       BetterPlayerConfiguration(
//         autoPlay: true,
//         looping: true,
//         aspectRatio: 16 / 9,
//         fit: BoxFit.contain, // 确保视频按比例缩放，不被拉伸
//         controlsConfiguration: const BetterPlayerControlsConfiguration(
//           //    enableFullscreen: true,
//           enableSkips: true,
//         ),
//       ),
//       betterPlayerDataSource: dataSource,
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return BetterPlayer(controller: _betterPlayerController);
//   }

//   @override
//   void dispose() {
//     _betterPlayerController.dispose();
//     super.dispose();
//   }
// }

import 'package:flutter/material.dart';
import 'package:better_player/better_player.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:shoot_z/gen/assets.gen.dart';
import 'package:shoot_z/pages/tab1Home/video_player/video_player_logic.dart';
import 'package:shoot_z/widgets/MyAppBar.dart';
import 'package:ui_packages/ui_packages.dart';

class VideoPlayerPage extends StatefulWidget {
  const VideoPlayerPage({super.key});

  @override
  State<VideoPlayerPage> createState() => _VerticalVideoPlayerState();
}

class _VerticalVideoPlayerState extends State<VideoPlayerPage> {
  bool _isFullScreen = false;
  bool _isPortraitVideo = true;
  final logic = Get.put(VideoPlayerLogic());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: const Text(""),
        actions: [
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              logic.share();
            },
            child: SizedBox(
              width: 40.w,
              height: 40.w,
              child: WxAssets.images.icShare.image(width: 21.w, height: 21.w),
            ),
          )
        ],
      ),

      body: Obx(() {
        return logic.isFrist.value
            ? const Center(child: CircularProgressIndicator())
            :
            // Container(
            //   width: double.infinity,
            //   color: Colors.red,
            //   child: LayoutBuilder(
            //     builder: (context, constraints) {
            //       // 计算最佳显示尺寸
            //       double width, height;

            //       if (logic.isHorizontal.value) {
            //         // 横屏视频
            //         width = constraints.maxWidth;
            //         height = width / logic.aspectRatio.value;
            //       } else {
            //         // 竖屏视频
            //         height = constraints.maxHeight;
            //         width = height * logic.aspectRatio.value;
            //       }
            //       log("heightheight=$height-width=$width");
            //       return Center(
            //         child: SizedBox(
            //           width: width,
            //           height: height,
            //           child: BetterPlayer(controller: logic.betterPlayerController),
            //         ),
            //       );
            //     },
            //   ),
            // ),

            //  (!logic
            //         .betterPlayerController.videoPlayerController!.value.initialized)
            //     ? const Center(child: CircularProgressIndicator())
            //     :

            //     Stack(
            //   alignment: Alignment.center,
            //   children: [
            //     // 视频播放区域
            //     // SizedBox(
            //     //   width: double.infinity,
            //     //   height: ScreenUtil().screenWidth / 375 * 211,
            //     //   child: AspectRatio(
            //     //     aspectRatio: 211 / 375,
            //     //     child:
            //     //         BetterPlayer(controller: logic.betterPlayerController),
            //     //   ),
            //     // ),
            //     Container(
            //       width: double.infinity,
            //       color: Colors.red,
            //       child: LayoutBuilder(
            //         builder: (context, constraints) {
            //           // 计算最佳显示尺寸
            //           double width, height;

            //           if (logic.isHorizontal.value) {
            //             // 横屏视频
            //             width = constraints.maxWidth;
            //             height = width / logic.aspectRatio.value;
            //           } else {
            //             // 竖屏视频
            //             height = constraints.maxHeight;
            //             width = height * logic.aspectRatio.value;
            //           }

            //           return Center(
            //             child: SizedBox(
            //               width: width,
            //               height: height,
            //               child:
            //                   BetterPlayer(controller: logic.betterPlayerController),
            //             ),
            //           );
            //         },
            //       ),
            //     ),
            // logic.isHorizontal.value
            //     ? Container(
            //         width: double.infinity,
            //         color: Colors.red,
            //         height: ScreenUtil().screenWidth / 16 * 9,
            //         alignment: Alignment.center,
            //         child: AspectRatio(
            //           aspectRatio: 16 * 9,
            //           child: BetterPlayer(
            //               controller: logic.betterPlayerController),
            //         ),
            //       )
            //     :
            Column(
                children: [
                  Expanded(
                    child: Container(
                      // padding: EdgeInsets.only(top: (Platform.isIOS?Screen.topSafeHeight:0.0)),
                      width: double.infinity,
                      height: ScreenUtil().screenHeight,
                      child: AspectRatio(
                        aspectRatio: ScreenUtil().screenWidth /
                            ScreenUtil().screenHeight,
                        child: BetterPlayer(
                            controller: logic.betterPlayerController),
                        //),
                      ),
                    ),
                  ),
                  logic.type.value == "0"
                      ? Container(
                          width: double.infinity,
                          padding: EdgeInsets.symmetric(
                              horizontal: 15.w, vertical: 15.w),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  // MyImage(
                                  //   logic.featuredListModel.value.coverUrl ?? "",
                                  //   width: 28.w,
                                  //   height: 28.w,
                                  //   radius: 15.r,
                                  //   isAssetImage: false,
                                  //   errorImage: "my_team_head4.png",
                                  // ),
                                  // SizedBox(
                                  //   width: 5.w,
                                  // ),
                                  Expanded(
                                    child: Text(
                                      logic.featuredListModel.value.title ?? "",
                                      style: TextStyles.regular.copyWith(
                                          fontSize: 14.sp,
                                          color: Colours.white,
                                          fontWeight: FontWeight.w600),
                                    ),
                                  )
                                ],
                              ),
                              SizedBox(
                                height: 15.w,
                              ),
                              Text(
                                logic.featuredListModel.value.content ?? "",
                                style: TextStyles.regular.copyWith(
                                  fontSize: 12.sp,
                                  color: Colours.white,
                                ),
                              ),
                              SizedBox(
                                height: 40.w,
                              ),
                            ],
                          ),
                        )
                      : SizedBox()
                ],
              );
      }),
      //     // // 自定义全屏按钮
      //     // if (!_isFullScreen)
      //     //   ElevatedButton(
      //     //     onPressed: _enterFullScreen,
      //     //     child: const Text('进入全屏'),
      //     //   ),

      //     Positioned(
      //       bottom: 10,
      //       child: Obx(() {
      //         return logic.type.value == "0"
      //             ? Container(
      //                 width: double.infinity,
      //                 height: 100.w,
      //                 padding: EdgeInsets.symmetric(horizontal: 15.w),
      //                 child: Column(
      //                   mainAxisSize: MainAxisSize.min,
      //                   crossAxisAlignment: CrossAxisAlignment.start,
      //                   children: [
      //                     Row(
      //                       children: [
      //                         MyImage(
      //                           logic.featuredListModel.value.coverUrl ?? "",
      //                           width: 28.w,
      //                           height: 28.w,
      //                           radius: 15.r,
      //                           isAssetImage: false,
      //                           errorImage: "my_team_head4.png",
      //                         ),
      //                         SizedBox(
      //                           width: 5.w,
      //                         ),
      //                         Expanded(
      //                           child: Text(
      //                             logic.featuredListModel.value.title ?? "",
      //                             style: TextStyles.regular.copyWith(
      //                               fontSize: 14.sp,
      //                               color: Colours.white,
      //                             ),
      //                           ),
      //                         )
      //                       ],
      //                     ),
      //                     SizedBox(
      //                       height: 15.w,
      //                     ),
      //                     Text(
      //                       logic.featuredListModel.value.content ?? "",
      //                       style: TextStyles.regular.copyWith(
      //                         fontSize: 14.sp,
      //                         color: Colours.white,
      //                       ),
      //                     ),
      //                     SizedBox(
      //                       height: 60.w,
      //                     ),
      //                   ],
      //                 ),
      //               )
      //             : const SizedBox();
      //       }),
      //     )
      //   ],
      // ),
    );
  }

  void _enterFullScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FullScreenVerticalVideo(
          controller: logic.betterPlayerController,
          isPortraitVideo: _isPortraitVideo,
          aspectRatio: 211 / 375,
          onExit: () => setState(() => _isFullScreen = false),
        ),
      ),
    );
    setState(() => _isFullScreen = true);
  }
}

class FullScreenVerticalVideo extends StatefulWidget {
  final BetterPlayerController controller;
  final bool isPortraitVideo;
  final double aspectRatio;
  final VoidCallback onExit;

  const FullScreenVerticalVideo({
    super.key,
    required this.controller,
    required this.isPortraitVideo,
    required this.aspectRatio,
    required this.onExit,
  });

  @override
  State<FullScreenVerticalVideo> createState() =>
      _FullScreenVerticalVideoState();
}

class _FullScreenVerticalVideoState extends State<FullScreenVerticalVideo> {
  @override
  void initState() {
    super.initState();

    // 锁定屏幕方向为竖屏
    if (widget.isPortraitVideo) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    }

    // 隐藏状态栏和导航栏
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 视频播放器 - 确保真正全屏
          Positioned.fill(
            child: FittedBox(
              fit: BoxFit.contain,
              child: SizedBox(
                width: widget.isPortraitVideo
                    ? MediaQuery.of(context).size.height * widget.aspectRatio
                    : MediaQuery.of(context).size.width,
                height: widget.isPortraitVideo
                    ? MediaQuery.of(context).size.height
                    : MediaQuery.of(context).size.width / widget.aspectRatio,
                child: BetterPlayer(controller: widget.controller),
              ),
            ),
          ),

          // 退出按钮
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 10,
            child: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: _exitFullScreen,
            ),
          ),
        ],
      ),
    );
  }

  void _exitFullScreen() {
    // 恢复屏幕方向
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // 显示状态栏和导航栏
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    // 退出全屏
    Navigator.pop(context);
    widget.onExit();
  }

  @override
  void dispose() {
    // 恢复屏幕方向
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // 显示状态栏和导航栏
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    super.dispose();
  }
}
