plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
if (flutterVersionCode == null) {
    flutterVersionCode = "1"
}

def flutterVersionName = localProperties.getProperty("flutter.versionName")
if (flutterVersionName == null) {
    flutterVersionName = "1.0"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.shootZ.app.shoot_z"
    compileSdk = 34
    ndkVersion = flutter.ndkVersion

//    如果不设置 kotlinOptions，默认会使用 Kotlin 编译器的默认 JVM 目标版本，这可能导致与 Java 1.8 特性不兼容的错误。
    kotlinOptions {
        jvmTarget = "1.8"
    }

//    compileOptions 设置 Java 编译器的目标版本，kotlinOptions 设置 Kotlin 编译器的目标版本。两者应保持一致
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.shootZ.app.shoot_z"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdk = 24//flutter.minSdkVersion
        targetSdkVersion 34
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
        multiDexEnabled true
        // 添加 NDK 配置
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
    }
    dataBinding {
        enabled = true
    }


//    splits     {
//        abi {
//            enable true // 启用拆分 ABI 功能
//            reset()
//            include 'armeabi-v7a', 'arm64-v8a'
//            universalApk true
//        }
//    }

    // 强制让不同架构的 APK 使用相同的 versionCode
    applicationVariants.all { variant ->
        variant.outputs.each { output ->
//            def outputFileName = output.outputFile?.name
//            if (outputFileName?.contains("armeabi-v7a") || outputFileName?.contains("arm64-v8a")) {
                output.versionCodeOverride = 4016 //自定义版本号且分包都一致
//            }
        }
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
        debug {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        debug {
            // 暂时禁用代码压缩和资源压缩，以排除问题
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.debug
        }
        release {
            // 暂时禁用代码压缩和资源压缩，以排除问题
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.release
            // 关键修复：禁用资源混淆
            crunchPngs false
//            shrinkResources true
//            minifyEnabled true
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    // 确保原生库不被压缩
    packagingOptions {
        jniLibs {
            useLegacyPackaging = true // 对于Gradle 7.0+可能需要这个选项
        }
        resources {
            excludes += ['lib/*/libflutter.so', 'lib/*/libapp.so']
        }
        // 不压缩so文件
        doNotStrip '**/libflutter.so'
        doNotStrip '**/libapp.so'
        doNotStrip '**/libmediapipe_jni.so'
        // 排除不必要的文件
        exclude 'META-INF/proguard/androidx-annotations.pro'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
    }
    // 添加源集配置
    sourceSets {
        main {
            jniLibs.srcDirs = ['src/main/jniLibs']
        }
    }
    buildFeatures {
        viewBinding true
    }
    androidResources {
        noCompress 'tflite'
    }
}
project.ext.ASSET_DIR = projectDir.toString() + '/src/main/assets'

dependencies {
    implementation 'com.amap.api:location:5.2.0' // 高德定位库
    // ===== [ Kotlin 语言支持库 ] =====
    // Kotlin 核心扩展库：提供 Kotlin 扩展功能
    implementation 'androidx.core:core-ktx:1.6.0'
    // Kotlin 标准库：提供 Kotlin 基础功能
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    // Kotlin 协程库：提供异步编程支持¬
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.0'
    // ===== [ UI 和组件库 ] =====
    // AppCompat：支持 Material Design 的向后兼容 UI 组件
    implementation 'androidx.appcompat:appcompat:1.3.1'
    // Material Design：Google 的 Material UI 组件
    implementation 'com.google.android.material:material:1.9.0'
    // ConstraintLayout：Android 的灵活布局系统
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'

    // ===== [ 生命周期组件 ] =====
    // 生命周期运行时：管理 Android 组件的生命周期
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.3.1'

    // ===== [ Fragment 和导航 ] =====
    // Fragment KTX：Kotlin 扩展为 Fragment
    implementation 'androidx.fragment:fragment-ktx:1.5.4'
    // 本地广播管理器：应用内局部广播
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0'
    implementation 'com.google.android.odml:image:1.0.0-beta1'
    implementation 'androidx.activity:activity:1.7.1'

    // 导航组件：简化应用内导航
    def nav_version = "2.3.5"
    // 导航 Fragment KTX
    implementation "androidx.navigation:navigation-fragment-ktx:$nav_version"
    // 导航 UI KTX
    implementation "androidx.navigation:navigation-ui-ktx:$nav_version"

    // ===== [ CameraX 相机库 ] =====
    // CameraX 库（所有组件均使用相同版本）
    def camerax_version = '1.3.0' ///1.1.0
    // CameraX 核心：提供基本相机功能
    implementation "androidx.camera:camera-core:$camerax_version"
    // CameraX Camera2：Camera2 API 扩展
    implementation "androidx.camera:camera-camera2:$camerax_version"
    // CameraX 生命周期：自动处理相机生命周期
    implementation "androidx.camera:camera-lifecycle:$camerax_version"
    // CameraX 视图：预览视图组件
    implementation "androidx.camera:camera-view:$camerax_version"

    implementation "androidx.camera:camera-video:$camerax_version"
    implementation 'com.google.code.gson:gson:2.8.9'
    // ===== [ Window 管理 ] =====
    // WindowManager：支持不同尺寸设备（如折叠屏）
    implementation 'androidx.window:window:1.0.0-alpha09'

    // ===== [ MediaPipe 视觉任务库 ] =====
    // MediaPipe Tasks Vision：用于计算机视觉任务（对象检测、分类等）
    implementation 'com.google.mediapipe:tasks-vision:0.10.16'
//    // ===== [ 单元测试 ] =====
//    testImplementation 'androidx.test.ext:junit:1.1.3'       // JUnit 测试框架扩展
//    testImplementation 'androidx.test:rules:1.4.0'         // 测试规则支持
//    testImplementation 'androidx.test:runner:1.4.0'        // 测试运行器
//    testImplementation 'androidx.test.espresso:espresso-core:3.4.0' // UI 测试
//    testImplementation 'org.robolectric:robolectric:4.4'    // 模拟 Android 环境的测试框架
//
//    // ===== [ 仪器化测试（设备上测试）] =====
//    androidTestImplementation "androidx.test.ext:junit:1.1.3"    // 仪器化 JUnit
//    androidTestImplementation "androidx.test:core:1.4.0"        // 测试核心组件
//    androidTestImplementation "androidx.test:rules:1.4.0"        // 测试规则
//    androidTestImplementation "androidx.test:runner:1.4.0"       // 测试运行器
//    androidTestImplementation "androidx.test.espresso:espresso-core:3.4.0" // UI 测试
    //implementation 'com.google.mediapipe:tasks-vision:0.10.0'
    // Permission handling - 权限管理
    implementation 'com.karumi:dexter:6.2.3'
    implementation 'com.github.microshow:RxFFmpeg:4.9.0'
}

flutter {
    source = "../.."
}
